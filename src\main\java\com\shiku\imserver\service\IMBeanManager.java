
package com.shiku.imserver.service;


import com.mongodb.MongoClient;
import com.mongodb.MongoClientURI;
import com.shiku.imserver.ImServerGroupContext;
import com.shiku.imserver.cluster.ImClusterServiceIfc;
import com.shiku.imserver.common.AbstractService;
import com.shiku.imserver.common.ImConfig;
import com.shiku.imserver.message.processor.MessageProcess;
import com.shiku.imserver.repository.GroupRepository;
import com.shiku.imserver.repository.MessageRepository;
import com.shiku.imserver.repository.NewFirendRepository;
import com.shiku.imserver.repository.ServiceRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class IMBeanManager
        extends AbstractService {
    public static Logger logger = LoggerFactory.getLogger(IMBeanManager.class);
    private MessageProcess messageProcess = new MessageProcess();

    private MessageRepository messageRepository = new MessageRepository();

    private GroupRepository groupRepository = new GroupRepository();

    private NewFirendRepository newFirendRepository = new NewFirendRepository();

    private RedisService redisService = new RedisService();

    private RocketmqService rocketmqService = new RocketmqService();

    private ReceiptLogicService receiptLogicService = new ReceiptLogicService();

    private ServiceRepository serviceRepository = new ServiceRepository();


    private ImServerGroupContext groupContext;

    private MongoClient mongoClient;

    private ImConfig imConfig = null;

    private ImClusterServiceIfc imClusterService;


    @Override
    public boolean initialize() {

        try {

            MongoClientURI uri = new MongoClientURI(IMBeanUtils.getImconfig().getMongoConfig().getUri());

            this.mongoClient = new MongoClient(uri);

        } catch (Exception e) {

            logger.error(" init mongoClient Exception  {} ", e.getMessage());

            return false;

        }

        this.serviceRepository.initialize();


        this.messageProcess.initialize();

        this.messageRepository.initialize();

        this.groupRepository.initialize();

        this.newFirendRepository.initialize();


        this.redisService.initialize();

        this.rocketmqService.initialize();

        this.receiptLogicService.initialize();

        if (this.imConfig.isCluster()) {

            try {

                System.out.println(System.getProperty("java.ext.dirs"));


                this
                        .imClusterService = (ImClusterServiceIfc) Thread.currentThread().getContextClassLoader().loadClass("com.shiku.imserver.cluster.ImClusterService").newInstance();


                this.imClusterService.initialize();

            } catch (ClassNotFoundException e) {

                logger.error("集群组建 初始化失败 找不到集群实现类 com.shiku.imserver.cluster.ImClusterService", e);

                System.exit(0);

            } catch (Exception e) {

                logger.error(e.getMessage(), e);

                System.exit(0);

            }

        }

        return true;

    }


    @Override
    public boolean startupAfter() {

        try {

            if (null != this.imClusterService) {
                this.imClusterService.startupAfter();
            }

        } catch (Exception e) {

            logger.error(e.getMessage());

        }

        return true;

    }


    public ImConfig getImConfig() {

        return this.imConfig;

    }


    public void setImConfig(ImConfig imConfig) {

        this.imConfig = imConfig;

    }


    public MessageProcess getMessageProcess() {

        return this.messageProcess;

    }


    public MongoClient getMongoClient() {

        return this.mongoClient;

    }


    public RedisService getRedisService() {

        return this.redisService;

    }


    public MessageRepository getMessageRepository() {

        return this.messageRepository;

    }


    public GroupRepository getGroupRepository() {

        return this.groupRepository;

    }


    public NewFirendRepository getNewFirendRepository() {

        return this.newFirendRepository;

    }


    public RocketmqService getRocketmqService() {

        return this.rocketmqService;

    }


    public ReceiptLogicService getReceiptLogicService() {

        return this.receiptLogicService;

    }


    public ServiceRepository getServiceRepository() {

        return this.serviceRepository;

    }


    public ImServerGroupContext getGroupContext() {

        return this.groupContext;

    }


    public void setGroupContext(ImServerGroupContext groupContext) {

        this.groupContext = groupContext;

    }


    public ImClusterServiceIfc getImClusterService() {

        return this.imClusterService;

    }


    public void setImClusterService(ImClusterServiceIfc imClusterService) {

        this.imClusterService = imClusterService;

    }

}


