package com.shiku.commons.constants;

public class CommConstants {
    public static boolean isDebug = true;
    public static final String PAGE_INDEX = "0";
    public static final String PAGE_SIZE = "15";
    public static final int MOENY_ADD = 1;
    public static final int MOENY_REDUCE = 2;
    public static final double LBS_KM = 111.01D;
    public static final int LBS_DISTANCE = 50;
    public static final int MIN_USERID = 100000;

    public CommConstants() {
    }

    public interface ResultCode {
        int Success = 1;
        int Failure = 0;
        int ParamsAuthFail = 1010101;
        int ParamsLack = 1010102;
        int InternalException = 1020101;
        int Link_Expired = 1020102;
        int TokenEillegal = 1030101;
        int TokenInvalid = 1030102;
        int AUTH_FAILED = 1030103;
        int AccountNotExist = 1040101;
        int AccountOrPasswordIncorrect = 1040102;
        int OldPasswordIsWrong = 1040103;
        int VerifyCodeErrOrExpired = 1040104;
        int SedMsgFail = 1040105;
        int ManySedMsg = 1040106;
        int PhoneRegistered = 1040107;
        int InsufficientBalance = 1040201;
        int NullImgCode = 1040215;
        int ImgCodeError = 1040216;
        int NotSelectPayType = 1040301;
        int AliPayCallBack_FAILED = 1040302;
        int NotPermissionDelete = 1040303;
        int ACCOUNT_IS_LOCKED = 1040304;
        int UNBindingTelephone = 1040305;
        int SdkLoginNotExist = 1040306;
    }

    public interface Expire {
        int DAY1 = 86400;
        int DAY7 = 604800;
        int HOUR12 = 43200;
        int HOUR = 3600;
    }

    public interface PayType {
        int ALIPAY = 1;
        int WXPAY = 2;
        int BALANCEAY = 3;
        int SYSTEMPAY = 4;
    }

    public interface OrderStatus {
        int CREATE = 0;
        int END = 1;
        int DELETE = -1;
    }

    public interface DB_REMAINDER {
        int DEFAULT = 10000;
    }
}
