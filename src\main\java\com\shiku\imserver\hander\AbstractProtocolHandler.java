
package com.shiku.imserver.hander;

import com.shiku.imserver.common.constant.IMLoggers;
import com.shiku.imserver.common.packets.ImPacket;
import com.shiku.imserver.service.IMBeanUtils;

import java.nio.ByteBuffer;

import org.tio.core.ChannelContext;
import org.tio.core.exception.AioDecodeException;
import org.tio.core.intf.Packet;

public abstract class AbstractProtocolHandler extends Im<PERSON>erverHandler {
    public AbstractProtocolHandler() {
    }

    @Override
    public void handler(Packet packet, ChannelContext channelContext) throws Exception {
        ImPacket impacket = (ImPacket) packet;
        if (0 == impacket.getCommand()) {
            IMLoggers.handler.error(" command is 0 ");
        } else {
            IMBeanUtils.getMessageProcess().dispatch(impacket, channelContext);
        }
    }

    @Override
    public Packet decode(ByteBuffer buffer, int limit, int position, int readableLength, ChannelContext channelContext) throws AioDecodeException {
        return this.decode(buffer, channelContext);
    }

    public abstract Packet decode(ByteBuffer var1, ChannelContext var2) throws AioDecodeException;
}
