# 使用OpenJDK 8作为基础镜像
FROM openjdk:8-jre-alpine

# 设置工作目录
WORKDIR /app

# 安装必要的工具
RUN apk add --no-cache curl bash

# 创建应用目录
RUN mkdir -p /app/config /app/logs /app/lib

# 复制JAR文件
COPY target/shiku-0.0.1-SNAPSHOT.jar /app/shiku-im.jar

# 复制配置文件
COPY src/main/resources/imserver.properties /app/config/
COPY src/main/resources/logback.xml /app/config/
COPY src/main/resources/socket.jks /app/config/

# 设置环境变量
ENV JAVA_OPTS="-Xms1g -Xmx2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
ENV APP_OPTS="--spring.config.location=file:/app/config/imserver.properties"

# 暴露端口
EXPOSE 5666 5280 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
  CMD curl -f http://localhost:8080/health || exit 1

# 启动脚本
COPY docker-entrypoint.sh /app/
RUN chmod +x /app/docker-entrypoint.sh

# 启动应用
ENTRYPOINT ["/app/docker-entrypoint.sh"]
