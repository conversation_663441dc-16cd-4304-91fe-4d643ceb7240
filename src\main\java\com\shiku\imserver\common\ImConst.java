package com.shiku.imserver.common;

public interface ImConst {
    public static final String authkey = "shiku";

    public static final int SERVER_PORT = 8888;

    public static final String RESOURCE = "resource";

    public static final String CHARSET = "utf-8";

    public static final String TO = "to";

    public static final String CHANNEL = "channel";

    public static final String PACKET = "packet";

    public static final String STATUS = "status";

    public static final String HTTP_REQUEST = "httpRequest";

    public static final String CHAT_QUEUE = "chat_queue";

    public static final String STORE = "store";

    public static final String PUSH = "push";

    public static final String CHAT = "chat";

    public static final String GROUP = "group";

    public static final String USER = "user";

    public static final String TERMINAL = "terminal";

    public static final String INFO = "info";

    public static final String FRIENDS = "friends";

    public static final String ONLINE = "online";

    public static final String OFFLINE = "offline";

    public static final String ON = "on";

    public static final String OFF = "off";

    public static final String JIM = "Shiku IM Server";

    public static final String CONVERTOR = "convertor";

    public static final String BASE_ASYNC_CHAT_MESSAGE_PROCESSOR = "base_async_chat_message_processor";
}


