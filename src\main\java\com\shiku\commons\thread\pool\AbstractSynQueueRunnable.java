
package com.shiku.commons.thread.pool;


import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.Executor;


public abstract class AbstractSynQueueRunnable<T>
        extends AbstractSynRunnable {
    protected ConcurrentLinkedQueue<T> msgQueue = new ConcurrentLinkedQueue<>();


    public AbstractSynQueueRunnable(Executor executor) {

        super(executor);

    }


    public boolean addMsg(T t) {

        if (isCanceled()) {

            return false;

        }


        return this.msgQueue.offer(t);

    }


    public void clearMsgQueue() {

        this.msgQueue.clear();

    }


    @Override
    public boolean isNeededExecute() {

        return !this.msgQueue.isEmpty();

    }

}


