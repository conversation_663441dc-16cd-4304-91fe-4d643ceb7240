
package com.shiku.utils;


import java.math.BigDecimal;
import java.math.RoundingMode;


public class Money {

    public static String fromYuan(String money) {

        return (new BigDecimal(money)).stripTrailingZeros().toString();

    }


    public static String fromCent(String money) {

        return fromCent(Integer.valueOf(money).intValue());

    }


    public static String fromCent(int money) {

        BigDecimal ret = new BigDecimal(money);

        ret = ret.divide(new BigDecimal(100), 2, RoundingMode.UNNECESSARY);

        return ret.stripTrailingZeros().toString();

    }

}


