package com.shiku.commons.task;

import java.util.concurrent.ScheduledFuture;

public abstract class TimerTask implements Runnable {
    private ScheduledFuture<?> future = null;

    public TimerTask() {
    }

    public void setScheduledFuture(ScheduledFuture<?> future) {
        this.future = future;
    }

    public boolean isScheduled() {
        return this.future != null && !this.future.isCancelled() && !this.future.isDone();
    }

    public void cancel() {
        this.cancel(false);
    }

    public void cancel(boolean mayInterruptIfRunning) {
        if (this.future != null && !this.future.isDone()) {
            this.future.cancel(mayInterruptIfRunning);
        }

    }
}
