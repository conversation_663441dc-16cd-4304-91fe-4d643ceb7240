
package com.shiku.utils;


public class WebNetEncode {

    public static String toHexString(String s) {

        String str = "";

        for (int i = 0; i < s.length(); i++) {

            int ch = s.charAt(i);

            String s4 = Integer.toHexString(ch);

            str = str + s4;

        }

        return "0x" + str;

    }


    public static String toStringHex(String s) {

        if ("0x".equals(s.substring(0, 2))) {

            s = s.substring(2);

        }

        byte[] baKeyword = new byte[s.length() / 2];

        for (int i = 0; i < baKeyword.length; i++) {

            try {

                baKeyword[i] = (byte) (0xFF & Integer.parseInt(s.substring(i * 2, i * 2 + 2), 16));

            } catch (Exception e) {

                e.printStackTrace();

            }

        }


        try {

            s = new String(baKeyword, "GBK");

        } catch (Exception e1) {

            e1.printStackTrace();

        }

        return s;

    }


    public static final String bytesToHexString(byte[] bArray) {

        StringBuffer sb = new StringBuffer(bArray.length);


        for (int i = 0; i < bArray.length; i++) {

            String sTemp = Integer.toHexString(0xFF & bArray[i]);

            if (sTemp.length() < 2) {
                sb.append(0);
            }

            sb.append(sTemp.toUpperCase());

        }

        return sb.toString();

    }


    public static String encodeHexStr(int dataCoding, String realStr) {

        String strhex = "";

        try {

            byte[] bytSource = null;

            if (dataCoding == 15) {

                bytSource = realStr.getBytes("GBK");

            } else if (dataCoding == 3) {

                bytSource = realStr.getBytes("ISO-8859-1");

            } else if (dataCoding == 8) {

                bytSource = realStr.getBytes("UTF-16BE");

            } else {

                bytSource = realStr.getBytes("ASCII");

            }

            strhex = bytesToHexString(bytSource);

        } catch (Exception e) {

            e.printStackTrace();

        }

        return strhex;

    }


    public static String decodeHexStr(int dataCoding, String hexStr) {

        String strReturn = "";

        try {

            int len = hexStr.length() / 2;

            byte[] bytSrc = new byte[len];

            for (int i = 0; i < len; i++) {

                String s = hexStr.substring(i * 2, 2);

                bytSrc[i] = Byte.parseByte(s, 512);

                Byte.parseByte(s, i);

            }


            if (dataCoding == 15) {

                strReturn = new String(bytSrc, "GBK");

            } else if (dataCoding == 3) {

                strReturn = new String(bytSrc, "ISO-8859-1");

            } else if (dataCoding == 8) {

                strReturn = new String(bytSrc, "UTF-16BE");

            } else {


                strReturn = new String(bytSrc, "ASCII");

            }


        } catch (Exception e) {

            e.printStackTrace();

        }

        return strReturn;

    }

}


