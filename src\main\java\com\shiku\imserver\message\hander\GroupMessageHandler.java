
package com.shiku.imserver.message.hander;

import com.shiku.imserver.common.annotation.MessageCommandAnnotation;
import com.shiku.imserver.common.constant.KConstants;
import com.shiku.imserver.common.hander.AbstractMessageHandler;
import com.shiku.imserver.common.message.ChatMessage;
import com.shiku.imserver.common.message.ExitGroupMessage;
import com.shiku.imserver.common.message.GroupMessageResp;
import com.shiku.imserver.common.message.JoinGroupMessage;
import com.shiku.imserver.common.message.PacketVO;
import com.shiku.imserver.common.packets.ImPacket;
import com.shiku.imserver.common.proto.MessageProBuf.ExitGroupMessageProBuf;
import com.shiku.imserver.common.proto.MessageProBuf.GroupMessageRespProBuf;
import com.shiku.imserver.common.proto.MessageProBuf.JoinGroupMessageProBuf;
import com.shiku.imserver.common.utils.ProBufUtils;
import com.shiku.imserver.common.utils.StringUtils;
import com.shiku.imserver.message.MessageFactory;
import com.shiku.imserver.service.IMBeanUtils;

import java.util.Iterator;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.core.ChannelContext;
import org.tio.core.Tio;

public class GroupMessageHandler extends AbstractMessageHandler {
    private static final Logger logger = LoggerFactory.getLogger(GroupMessageHandler.class);

    public GroupMessageHandler() {
    }

    @MessageCommandAnnotation(
            command = 20
    )
    public PacketVO joinGroupHandler(ImPacket packet, ChannelContext channelContext) {
        try {
            JoinGroupMessage message = (JoinGroupMessage) ProBufUtils.decoderMessageBody(packet.getBytes(), JoinGroupMessageProBuf.getDescriptor(), JoinGroupMessage.class);
            String jid = message.getJid();
            long seconds = message.getSeconds();
            String from = message.getMessageHead().getFrom();
            if (KConstants.isDebug) {
                logger.info(" join group userId > {}  jid {}  seconds {}", new Object[]{from, jid, seconds});
            }

            GroupMessageResp resultMessage = MessageFactory.createGroupRequestResult(message, jid, false);
            ImPacket result = null;

            try {
                if (!StringUtils.isEmpty(jid)) {
                    Tio.bindGroup(channelContext, jid);
                }

                resultMessage.setStatus((byte) 1);
            } catch (Exception var14) {
                var14.printStackTrace();
                resultMessage.setStatus((byte) -1);
            }

            result = ProBufUtils.encodeImPacket(resultMessage, GroupMessageRespProBuf.getDescriptor());
            result.setCommand((short) 22);
            Tio.send(channelContext, result);
            if (0L < seconds) {
                List<ChatMessage> historyMessage = IMBeanUtils.getMessageRepository().loadGroupHistoryMessage(jid, seconds);
                ImPacket imPacket = null;
                Iterator var12 = historyMessage.iterator();

                while (var12.hasNext()) {
                    ChatMessage chatMessage = (ChatMessage) var12.next();
                    chatMessage.getMessageHead().setOffline(true);
                    imPacket = MessageFactory.convertToImPacket(chatMessage);
                    if (null != imPacket) {
                        Tio.send(channelContext, imPacket);
                    }
                }
            }
        } catch (Exception var15) {
            var15.printStackTrace();
        }

        return null;
    }

    @MessageCommandAnnotation(
            command = 21
    )
    public PacketVO exitGroupHandler(ImPacket packet, ChannelContext channelContext) throws Exception {
        PacketVO result = null;

        try {
            ExitGroupMessage message = (ExitGroupMessage) ProBufUtils.decoderMessageBody(packet.getBytes(), ExitGroupMessageProBuf.getDescriptor(), ExitGroupMessage.class);
            String jid = message.getJid();
            String from = message.getMessageHead().getFrom();
            if (KConstants.isDebug) {
                logger.info(" join group userId > {}  jid {}", from, jid);
            }

            GroupMessageResp resultMessage = MessageFactory.createGroupRequestResult(message, jid, true);

            try {
                if (!StringUtils.isEmpty(jid)) {
                    Tio.unbindGroup(jid, channelContext);
                }

                resultMessage.setStatus((byte) 1);
            } catch (Exception var9) {
                var9.printStackTrace();
                resultMessage.setStatus((byte) -1);
            }

            result = ProBufUtils.encodePacketVO(resultMessage, GroupMessageRespProBuf.getDescriptor());
            result.setCmd((short) 22);
        } catch (Exception var10) {
            var10.printStackTrace();
        }

        return result;
    }
}
