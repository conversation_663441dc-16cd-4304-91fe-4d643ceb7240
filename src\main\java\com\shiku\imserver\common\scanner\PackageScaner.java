
package com.shiku.imserver.common.scanner;


import com.shiku.imserver.common.constant.IMLoggers;

import java.io.File;
import java.net.JarURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;

import org.slf4j.Logger;


public class PackageScaner {
    public static final Logger logger = IMLoggers.serverLog;


    public static String[] scanNamespaceFiles(String namespace, String fileext, boolean isReturnCanonicalPath) {

        return scanNamespaceFiles(namespace, fileext, isReturnCanonicalPath, false);

    }


    public static String[] scanNamespaceFiles(String namespace, String fileext, boolean isReturnCanonicalPath, boolean checkSub) {

        String respath = namespace.replace('.', '/');

        respath = respath.replace('.', '/');


        List<String> tmpNameList = new ArrayList<>();


        try {

            URL url = null;

            logger.info("scan url path " + respath);

            if (!respath.startsWith("/")) {

                url = PackageScaner.class.getResource("/" + respath);

            } else {

                url = PackageScaner.class.getResource(respath);

            }
            logger.info("scan url  " + url);

            URLConnection tmpURLConnection = url.openConnection();


            if (tmpURLConnection instanceof JarURLConnection) {


                JarURLConnection tmpJarURLConnection = (JarURLConnection) tmpURLConnection;


                JarFile jarFile = tmpJarURLConnection.getJarFile();

                Enumeration<JarEntry> entrys = jarFile.entries();

                while (entrys.hasMoreElements()) {


                    JarEntry tmpJarEntry = entrys.nextElement();

                    if (!tmpJarEntry.isDirectory()) {


                        String tmpItemName = tmpJarEntry.getName();

                        if (tmpItemName.indexOf('$') < 0 && (fileext == null || tmpItemName
                                .endsWith(fileext))) {


                            int tmpPos = tmpItemName.lastIndexOf('/');

                            if (tmpPos > 0) {


                                String tmpPath = tmpItemName.substring(0, tmpPos);

                                if (checkSub) {

                                    if (tmpPath.startsWith(respath)) {


                                        String r = tmpItemName.substring(respath.length() + 1).replaceAll("/", ".");

                                        tmpNameList.add(r);

                                    }
                                    continue;

                                }

                                if (respath.equals(tmpPath)) {

                                    tmpNameList.add(tmpItemName.substring(tmpPos + 1));

                                }

                            }

                        }

                    }

                }


                jarFile.close();

            } else if (tmpURLConnection instanceof sun.net.www.protocol.file.FileURLConnection) {


                File file = new File(url.getFile());

                if (file.exists() && file.isDirectory()) {


                    File[] fileArray = file.listFiles();

                    for (File f : fileArray) {


                        if (!f.isDirectory() || f.getName().indexOf(".") == -1) {

                            String tmpItemName;


                            if (isReturnCanonicalPath) {

                                tmpItemName = f.getCanonicalPath();

                            } else {

                                tmpItemName = f.getName();

                            }
                            if (f.isDirectory()) {

                                String[] inner = scanNamespaceFiles(namespace + "." + tmpItemName, fileext, isReturnCanonicalPath);

                                if (inner != null) {


                                    for (String i : inner) {

                                        if (i != null) {
                                            tmpNameList.add(tmpItemName + "." + i);
                                        }

                                    }

                                }

                            } else if (fileext == null || tmpItemName.endsWith(fileext)) {


                                tmpNameList.add(tmpItemName);

                            }


                        }


                    }

                } else {


                    logger.error("scaning stop,invalid package path:" + url.getFile());

                }


            }

        } catch (Exception e) {


            logger.error("scaning stop,invalid package path error" + e.toString());

        }


        if (tmpNameList.size() > 0) {


            String[] r = new String[tmpNameList.size()];

            tmpNameList.toArray(r);

            tmpNameList.clear();

            return r;

        }

        return null;

    }

}


