# Shiku IM 集群部署指南

## 概述

本文档详细说明了如何部署和管理 Shiku IM 服务器集群。集群架构包括：

- **3个 IM 服务器节点**：提供高可用的即时通讯服务
- **Zookeeper 集群**：服务发现和配置管理
- **MongoDB 副本集**：数据持久化存储
- **Redis 集群**：缓存和会话管理
- **RocketMQ 集群**：消息队列服务
- **Nginx 负载均衡器**：流量分发和负载均衡

## 系统要求

### 硬件要求
- **CPU**: 最少 8 核心 (推荐 16 核心)
- **内存**: 最少 16GB (推荐 32GB)
- **存储**: 最少 100GB SSD (推荐 500GB)
- **网络**: 千兆网卡

### 软件要求
- **操作系统**: Linux (Ubuntu 18.04+, CentOS 7+)
- **Docker**: 20.10+
- **Docker Compose**: 1.29+
- **Java**: OpenJDK 8+
- **Maven**: 3.6+

## 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd shiku
```

### 2. 构建应用
```bash
mvn clean package -DskipTests
```

### 3. 部署集群
```bash
chmod +x deploy-cluster.sh
./deploy-cluster.sh
```

### 4. 监控集群
```bash
chmod +x cluster-monitor.sh
./cluster-monitor.sh -m
```

## 详细部署步骤

### 1. 环境准备

#### 安装 Docker
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# CentOS/RHEL
sudo yum install -y docker
sudo systemctl start docker
sudo systemctl enable docker
```

#### 安装 Docker Compose
```bash
sudo curl -L "https://github.com/docker/compose/releases/download/1.29.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

### 2. 配置文件说明

#### imserver.properties
主要配置文件，包含所有服务的连接信息：

```properties
# 集群配置
isCluster=true
clusterLicenseDir=/opt/shiku/license

# MongoDB配置
mongoConfig.uri=*********************************************************************************************

# Redis配置
redisConfig.address=redis://redis-cluster-1:6379,redis://redis-cluster-2:6379,redis://redis-cluster-3:6379

# Zookeeper配置
zkConfig.connectStr=zk1:2181,zk2:2181,zk3:2181

# RocketMQ配置
mqConfig.nameAddr=rocketmq-nameserver1:9876;rocketmq-nameserver2:9876
```

### 3. 服务端口说明

| 服务 | 端口 | 说明 |
|------|------|------|
| IM Server TCP | 5666 | TCP 长连接服务 |
| IM Server WebSocket | 5280 | WebSocket 服务 |
| HTTP API | 80 | REST API 服务 |
| MongoDB | 27017-27019 | 数据库服务 |
| Redis | 6379-6381 | 缓存服务 |
| Zookeeper | 2181-2183 | 配置管理 |
| RocketMQ NameServer | 9876-9877 | 消息队列 |

## 集群管理

### 使用管理脚本
```bash
chmod +x cluster-manager.sh
./cluster-manager.sh
```

管理脚本提供以下功能：
1. 启动/停止/重启集群
2. 扩容/缩容 IM 服务器节点
3. 滚动更新服务
4. 数据备份/恢复
5. 查看集群状态和日志
6. 清理无用资源

### 手动管理命令

#### 启动集群
```bash
docker-compose -f docker-compose-cluster.yml up -d
```

#### 停止集群
```bash
docker-compose -f docker-compose-cluster.yml down
```

#### 查看服务状态
```bash
docker-compose -f docker-compose-cluster.yml ps
```

#### 查看服务日志
```bash
docker-compose -f docker-compose-cluster.yml logs -f [服务名]
```

#### 扩容 IM 服务器
```bash
docker-compose -f docker-compose-cluster.yml up -d --scale shiku-im=5
```

## 监控和维护

### 1. 健康检查

#### 检查服务状态
```bash
./cluster-monitor.sh -s
```

#### 实时监控
```bash
./cluster-monitor.sh -m
```

### 2. 性能监控

#### 查看资源使用
```bash
docker stats
```

#### 查看网络连接
```bash
netstat -tlnp | grep -E "(5666|5280|27017|6379|2181|9876)"
```

### 3. 日志管理

#### 查看应用日志
```bash
docker-compose -f docker-compose-cluster.yml logs -f shiku-im-1
```

#### 查看系统日志
```bash
tail -f /var/log/syslog
```

## 故障排除

### 常见问题

#### 1. 服务启动失败
- 检查端口是否被占用
- 检查配置文件是否正确
- 查看服务日志定位问题

#### 2. 数据库连接失败
- 检查 MongoDB 副本集状态
- 验证连接字符串和认证信息
- 检查网络连通性

#### 3. Redis 集群问题
- 检查 Redis 集群状态
- 验证密码配置
- 重新初始化集群

#### 4. 消息队列问题
- 检查 RocketMQ NameServer 状态
- 验证 Broker 配置
- 检查主题和消费者组

### 故障恢复

#### 1. 单节点故障
```bash
# 重启故障节点
docker-compose -f docker-compose-cluster.yml restart [服务名]
```

#### 2. 数据恢复
```bash
# 使用管理脚本恢复数据
./cluster-manager.sh
# 选择选项 8 进行数据恢复
```

#### 3. 完全重建
```bash
# 停止所有服务
docker-compose -f docker-compose-cluster.yml down -v

# 清理数据卷
docker volume prune -f

# 重新部署
./deploy-cluster.sh
```

## 安全配置

### 1. 网络安全
- 使用防火墙限制端口访问
- 配置 SSL/TLS 加密
- 设置 VPN 或专用网络

### 2. 认证安全
- 修改默认密码
- 使用强密码策略
- 定期轮换密钥

### 3. 数据安全
- 定期备份数据
- 加密敏感数据
- 设置访问控制

## 性能优化

### 1. JVM 调优
```bash
# 在 Dockerfile 中设置 JVM 参数
ENV JAVA_OPTS="-Xms2g -Xmx4g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
```

### 2. 数据库优化
- 配置 MongoDB 索引
- 调整连接池大小
- 优化查询语句

### 3. 缓存优化
- 配置 Redis 内存策略
- 设置合适的过期时间
- 使用 Redis 集群分片

## 扩展和升级

### 1. 水平扩展
- 增加 IM 服务器节点
- 扩展数据库副本
- 增加缓存节点

### 2. 垂直扩展
- 增加服务器资源
- 优化应用配置
- 调整系统参数

### 3. 版本升级
- 使用滚动更新策略
- 保持数据兼容性
- 做好回滚准备

## 联系支持

如有问题，请联系技术支持团队或查看项目文档。
