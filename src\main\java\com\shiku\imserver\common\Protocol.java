package com.shiku.imserver.common;

public interface Protocol {
    public static final byte HEARTBEAT_BYTE = -128;

    public static final byte HANDSHAKE_BYTE = -127;

    public static final byte VERSION = 1;

    public static final byte TCP = 1;

    public static final byte WEBSOCKET = 2;

    public static final byte HTTP = 3;

    public static final String COOKIE_NAME_FOR_SESSION = "jim-s";

    public static final int MAX_LENGTH_OF_BODY = 2202009;

    public static final int LEAST_HEADER_LENGHT = 4;

    public static final byte FIRST_BYTE_MASK_ENCRYPT = -128;

    public static final byte FIRST_BYTE_MASK_COMPRESS = 64;

    public static final byte FIRST_BYTE_MASK_HAS_SYNSEQ = 32;

    public static final byte FIRST_BYTE_MASK_4_BYTE_LENGTH = 16;

    public static final byte FIRST_BYTE_MASK_VERSION = 15;
}


