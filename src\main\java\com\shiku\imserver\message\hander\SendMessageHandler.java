
package com.shiku.imserver.message.hander;

import com.shiku.imserver.CoreService;
import com.shiku.imserver.common.annotation.MessageCommandAnnotation;
import com.shiku.imserver.common.constant.KConstants;
import com.shiku.imserver.common.hander.AbstractMessageHandler;
import com.shiku.imserver.common.message.ChatMessage;
import com.shiku.imserver.common.message.PacketVO;
import com.shiku.imserver.common.packets.ImPacket;
import com.shiku.imserver.common.tcp.TcpPacket;
import com.shiku.imserver.common.utils.ProBufUtils;
import com.shiku.imserver.common.utils.StringUtils;
import com.shiku.imserver.message.MessageFactory;
import com.shiku.imserver.service.IMBeanUtils;
import com.shiku.utils.StringUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.core.ChannelContext;
import org.tio.core.Tio;

public class SendMessageHandler extends AbstractMessageHandler {
    private static final Logger logger = LoggerFactory.getLogger(SendMessageHandler.class);

    public SendMessageHandler() {
    }

    @MessageCommandAnnotation(
            command = 10
    )
    public PacketVO sendMsgMessageHandler(ImPacket packet, ChannelContext channelContext) {
        try {
            ChatMessage message = (ChatMessage) ProBufUtils.decoderMessageBody(packet.getBytes(), com.shiku.imserver.common.proto.MessageProBuf.ChatMessage.getDescriptor(), ChatMessage.class);
            byte chatType = message.getMessageHead().getChatType();
            if (KConstants.isDebug) {
                logger.info(" process msg => {}", message.toString());
            }

            String keyWord;
            if (1 == message.getType() && IMBeanUtils.getImconfig().isFilterKeyWord()) {
                keyWord = IMBeanUtils.getServiceRepository().filterKeyWord(message);
                if (!StringUtil.isEmpty(keyWord)) {
                    logger.info(" filterKeyWord  true ", message.getContent());
                    PacketVO errorPacket = MessageFactory.createErrorPacket(message.getMessageHead(), (short) -2, keyWord);
                    return errorPacket;
                }
            }

            keyWord = null;
            ImPacket result;
            if (chatType == 3) {
                ChatMessage chatMessage = message.clone();
                chatMessage.getMessageHead().setChatType((byte) 1);
                TcpPacket tcpPacket = ProBufUtils.encodeTcpPacket(chatMessage, com.shiku.imserver.common.proto.MessageProBuf.ChatMessage.getDescriptor());
                tcpPacket.setCommand((short) 10);
                Tio.sendToAll(channelContext.groupContext, tcpPacket);
                result = MessageFactory.createSuccessIMPacket(message);
                Tio.send(channelContext, result);
                IMBeanUtils.getRocketmqService().offMessagePush(message);
                return null;
            }

            String from = message.getMessageHead().getFrom();
            String to = message.getMessageHead().getTo();
            if (StringUtils.isEmpty(from) || StringUtils.isEmpty(to)) {
                logger.error(" from or to is null");
                return null;
            }

            String sender = CoreService.parseBareUserId(from);
            String receiver = CoreService.parseBareUserId(to);
            String fromResource = CoreService.parseResource(from);
            packet.setMessage(message);
            if (chatType == 1) {
                result = MessageFactory.createSuccessIMPacket(message);
                Tio.send(channelContext, result);
                boolean online = CoreService.userIsOnline(channelContext.groupContext, receiver);
                if (KConstants.isDebug) {
                    logger.info(" 接收方 userId {} online => {}", receiver, online);
                }

                if (!online) {
                    IMBeanUtils.getRocketmqService().offMessagePush(message);
                    IMBeanUtils.getMessageRepository().storeChatOffMessage(message);
                } else if (sender.equals(receiver)) {
                    CoreService.sendToUserOtherResource(channelContext.groupContext, sender, channelContext.getId(), packet, false);
                } else {
                    CoreService.sendToUser(channelContext.groupContext, to, packet);
                }

                if (!sender.equals(receiver) && !"Server".equals(fromResource)) {
                    CoreService.sendToUserOtherResource(channelContext.groupContext, sender, channelContext.getId(), packet, false);
                }
            } else if (chatType == 2) {
                if ("Server".equals(fromResource)) {
                    result = MessageFactory.createSuccessIMPacket(message);
                    Tio.send(channelContext, result);
                }

                Tio.sendToGroup(channelContext.groupContext, to, packet);
                IMBeanUtils.getRocketmqService().offMessagePush(message);
            }

            IMBeanUtils.getMessageRepository().offerMessage(message);
        } catch (Exception var12) {
            var12.printStackTrace();
        }

        return null;
    }
}
