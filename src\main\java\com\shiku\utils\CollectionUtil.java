
package com.shiku.utils;


import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;


public class CollectionUtil {

    public static <T> boolean isEmpty(Collection<T> col) {

        if (col == null || col.isEmpty()) {

            return true;

        }


        return false;

    }


    public static <T> boolean isNotEmpty(Collection<T> col) {

        return !isEmpty(col);

    }


    public static <K, V> boolean isEmpty(Map<K, V> map) {

        if (map == null || map.isEmpty()) {

            return true;

        }


        return false;

    }


    public static <K, V> boolean isNotEmpty(Map<K, V> map) {

        return !isEmpty(map);

    }


    public static <T> List<T> removeRepeat(List<T> list) {

        if (isEmpty(list)) {

            return list;

        }


        List<T> result = new ArrayList<>();

        for (T e : list) {

            if (!result.contains(e)) {

                result.add(e);

            }

        }


        return result;

    }


    public static <T> String[] toArray(List<T> list) {

        if (isEmpty(list)) {

            return null;

        }


        String[] result = new String[list.size()];

        for (int i = 0; i < list.size(); i++) {

            result[i] = String.valueOf(list.get(i));

        }


        return result;

    }

}


