#!/bin/bash

echo "========== Shiku IM 集群监控脚本 =========="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查服务状态
check_service_status() {
    local service_name=$1
    local container_name=$2
    
    if docker ps --format "table {{.Names}}" | grep -q "^${container_name}$"; then
        echo -e "${service_name}: ${GREEN}运行中${NC}"
        return 0
    else
        echo -e "${service_name}: ${RED}已停止${NC}"
        return 1
    fi
}

# 检查端口连通性
check_port() {
    local host=$1
    local port=$2
    local service_name=$3
    
    if nc -z $host $port 2>/dev/null; then
        echo -e "${service_name} (${host}:${port}): ${GREEN}可访问${NC}"
        return 0
    else
        echo -e "${service_name} (${host}:${port}): ${RED}不可访问${NC}"
        return 1
    fi
}

# 获取容器资源使用情况
get_container_stats() {
    local container_name=$1
    
    if docker ps --format "table {{.Names}}" | grep -q "^${container_name}$"; then
        local stats=$(docker stats --no-stream --format "table {{.CPUPerc}}\t{{.MemUsage}}" $container_name | tail -n 1)
        echo -e "${container_name}: ${BLUE}${stats}${NC}"
    else
        echo -e "${container_name}: ${RED}容器未运行${NC}"
    fi
}

# 主监控循环
monitor_cluster() {
    while true; do
        clear
        echo "========== Shiku IM 集群状态监控 =========="
        echo "监控时间: $(date '+%Y-%m-%d %H:%M:%S')"
        echo ""
        
        echo "========== 基础设施服务状态 =========="
        echo "--- Zookeeper集群 ---"
        check_service_status "Zookeeper-1" "shiku_zk1_1"
        check_service_status "Zookeeper-2" "shiku_zk2_1"
        check_service_status "Zookeeper-3" "shiku_zk3_1"
        
        echo ""
        echo "--- MongoDB副本集 ---"
        check_service_status "MongoDB-1" "shiku_mongodb1_1"
        check_service_status "MongoDB-2" "shiku_mongodb2_1"
        check_service_status "MongoDB-3" "shiku_mongodb3_1"
        
        echo ""
        echo "--- Redis集群 ---"
        check_service_status "Redis-1" "shiku_redis-cluster-1_1"
        check_service_status "Redis-2" "shiku_redis-cluster-2_1"
        check_service_status "Redis-3" "shiku_redis-cluster-3_1"
        
        echo ""
        echo "--- RocketMQ ---"
        check_service_status "RocketMQ-NameServer-1" "shiku_rocketmq-nameserver1_1"
        check_service_status "RocketMQ-NameServer-2" "shiku_rocketmq-nameserver2_1"
        check_service_status "RocketMQ-Broker" "shiku_rocketmq-broker_1"
        
        echo ""
        echo "========== IM服务器集群状态 =========="
        check_service_status "Shiku-IM-1" "shiku_shiku-im-1_1"
        check_service_status "Shiku-IM-2" "shiku_shiku-im-2_1"
        check_service_status "Shiku-IM-3" "shiku_shiku-im-3_1"
        check_service_status "Nginx负载均衡器" "shiku_nginx_1"
        
        echo ""
        echo "========== 端口连通性检查 =========="
        check_port "localhost" "5666" "TCP服务"
        check_port "localhost" "5280" "WebSocket服务"
        check_port "localhost" "80" "HTTP API"
        check_port "localhost" "2181" "Zookeeper"
        check_port "localhost" "27017" "MongoDB"
        check_port "localhost" "6379" "Redis"
        check_port "localhost" "9876" "RocketMQ NameServer"
        
        echo ""
        echo "========== 容器资源使用情况 =========="
        get_container_stats "shiku_shiku-im-1_1"
        get_container_stats "shiku_shiku-im-2_1"
        get_container_stats "shiku_shiku-im-3_1"
        get_container_stats "shiku_nginx_1"
        
        echo ""
        echo "========== 集群健康检查 =========="
        
        # 检查MongoDB副本集状态
        if docker exec shiku_mongodb1_1 mongo --quiet --eval "rs.status().ok" 2>/dev/null | grep -q "1"; then
            echo -e "MongoDB副本集: ${GREEN}健康${NC}"
        else
            echo -e "MongoDB副本集: ${YELLOW}检查中...${NC}"
        fi
        
        # 检查Redis集群状态
        if docker exec shiku_redis-cluster-1_1 redis-cli --cluster check redis-cluster-1:6379 2>/dev/null | grep -q "OK"; then
            echo -e "Redis集群: ${GREEN}健康${NC}"
        else
            echo -e "Redis集群: ${YELLOW}检查中...${NC}"
        fi
        
        echo ""
        echo "按 Ctrl+C 退出监控"
        echo "刷新间隔: 10秒"
        
        sleep 10
    done
}

# 显示帮助信息
show_help() {
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -m, --monitor     启动实时监控"
    echo "  -s, --status      显示当前状态"
    echo "  -l, --logs        显示服务日志"
    echo "  -h, --help        显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 -m             # 启动实时监控"
    echo "  $0 -s             # 显示当前状态"
    echo "  $0 -l shiku-im-1  # 显示特定服务日志"
}

# 显示服务日志
show_logs() {
    local service_name=$1
    if [ -z "$service_name" ]; then
        echo "可用的服务:"
        docker-compose -f docker-compose-cluster.yml config --services
        echo ""
        read -p "请输入服务名称: " service_name
    fi
    
    if [ -n "$service_name" ]; then
        docker-compose -f docker-compose-cluster.yml logs -f --tail=100 $service_name
    fi
}

# 显示当前状态
show_status() {
    echo "========== 当前集群状态 =========="
    docker-compose -f docker-compose-cluster.yml ps
    echo ""
    echo "========== 容器资源使用 =========="
    docker stats --no-stream
}

# 主程序
case "$1" in
    -m|--monitor)
        monitor_cluster
        ;;
    -s|--status)
        show_status
        ;;
    -l|--logs)
        show_logs $2
        ;;
    -h|--help)
        show_help
        ;;
    *)
        echo "使用 -h 或 --help 查看帮助信息"
        show_status
        ;;
esac
