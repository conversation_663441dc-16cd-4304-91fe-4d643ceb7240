/*     */
package com.shiku.imserver.common;
/*     */
/*     */

import com.shiku.imserver.common.config.Config;
/*     */ import com.shiku.imserver.common.constant.KConstants;
/*     */ import com.shiku.imserver.common.utils.StringUtils;
/*     */ import com.shiku.imserver.common.ws.WsServerConfig;
/*     */ import java.util.Properties;
/*     */ import org.tio.core.GroupContext;
/*     */ import org.tio.core.intf.GroupListener;
/*     */ import org.tio.core.ssl.SslConfig;
/*     */ import org.tio.http.common.HttpConfig;

/*     */
/*     */
/*     */
/*     */
/*     */
/*     */ public class ImConfig
        /*     */ extends Config
        /*     */ {
    /*     */   private Properties properties;
    /*     */   private HttpConfig httpConfig;
    /*     */   private WsServerConfig wsServerConfig;
    /*     */   protected GroupContext groupContext;
    /*     */   protected GroupListener imGroupListener;

    /*     */
    /*     */
    public void setProperties(Properties properties) {
        /*  27 */
        this.properties = properties;
    }

    public void setHttpConfig(HttpConfig httpConfig) {
        this.httpConfig = httpConfig;
    }

    public void setWsServerConfig(WsServerConfig wsServerConfig) {
        this.wsServerConfig = wsServerConfig;
    }

    public void setGroupContext(GroupContext groupContext) {
        this.groupContext = groupContext;
    }

    public void setImGroupListener(GroupListener imGroupListener) {
        this.imGroupListener = imGroupListener;
    }

    public void setIsStore(String isStore) {
        this.isStore = isStore;
    }

    public void setCluster(boolean isCluster) {
        this.isCluster = isCluster;
    }

    public void setClusterLicenseDir(String clusterLicenseDir) {
        this.clusterLicenseDir = clusterLicenseDir;
    }

    public void setWssPort(int wssPort) {
        this.wssPort = wssPort;
    }

    public void setWebsocketSsl(boolean websocketSsl) {
        this.websocketSsl = websocketSsl;
    }

    public void setKeyStoreFile(String keyStoreFile) {
        this.keyStoreFile = keyStoreFile;
    }

    public void setSslPassword(String sslPassword) {
        this.sslPassword = sslPassword;
    }

    public void setIsSSL(String isSSL) {
        this.isSSL = isSSL;
    }

    public void setSslConfig(SslConfig sslConfig) {
        this.sslConfig = sslConfig;
    }

    public void setMongoConfig(MongoConfig mongoConfig) {
        this.mongoConfig = mongoConfig;
    }

    public void setRedisConfig(RedisConfig redisConfig) {
        this.redisConfig = redisConfig;
    }

    public void setMqConfig(MQConfig mqConfig) {
        this.mqConfig = mqConfig;
    }

    public void setZookeeperConfig(ZookeeperConfig zookeeperConfig) {
        this.zookeeperConfig = zookeeperConfig;
    }

    public void setPushUserStatus(boolean pushUserStatus) {
        this.pushUserStatus = pushUserStatus;
    }

    public void setSaveChatMsg(boolean saveChatMsg) {
        this.saveChatMsg = saveChatMsg;
    }

    public void setSaveMucMsg(boolean saveMucMsg) {
        this.saveMucMsg = saveMucMsg;
    }

    public void setServerToken(String serverToken) {
        this.serverToken = serverToken;
    }

    public void setFilterKeyWord(boolean filterKeyWord) {
        this.filterKeyWord = filterKeyWord;
    }

    public void setMessageHandlerNameSpace(String messageHandlerNameSpace) {
        this.messageHandlerNameSpace = messageHandlerNameSpace;
    }

    /*     */
    /*     */
    /*     */
    /*     */
    public Properties getProperties() {
        /*  32 */
        return this.properties;
        /*     */
    }

    /*     */
    /*     */
    /*     */
    public HttpConfig getHttpConfig() {
        /*  37 */
        return this.httpConfig;
        /*     */
    }

    /*     */
    /*     */
    public WsServerConfig getWsServerConfig() {
        /*  41 */
        return this.wsServerConfig;
        /*     */
    }

    /*     */
    /*     */
    /*     */
    /*     */
    public GroupContext getGroupContext() {
        /*  47 */
        return this.groupContext;
        /*     */
    }

    /*     */
    /*     */
    public GroupListener getImGroupListener() {
        /*  51 */
        return this.imGroupListener;
        /*     */
    }

    /*     */
    /*     */
    /*  55 */   protected String isStore = "off";

    public String getIsStore() {
        return this.isStore;
    }

    /*     */
    /*     */   protected boolean isCluster = false;
    protected String clusterLicenseDir;

    /*     */
    public boolean isCluster() {
        /*  59 */
        return this.isCluster;
        /*     */
    }

    public String getClusterLicenseDir() {
        /*  61 */
        return this.clusterLicenseDir;
        /*     */
    }

    /*  63 */   private int wssPort = 5261;
    protected boolean websocketSsl;
    protected String keyStoreFile;
    protected String sslPassword;

    public int getWssPort() {
        return this.wssPort;
    }

    /*     */
    public boolean isWebsocketSsl() {
        /*  65 */
        return this.websocketSsl;
        /*     */
    }

    public String getKeyStoreFile() {
        /*  67 */
        return this.keyStoreFile;
        /*     */
    }

    public String getSslPassword() {
        /*  69 */
        return this.sslPassword;
        /*     */
    }

    /*     */
    /*     */
    /*     */
    /*  74 */   protected String isSSL = "off";
    protected SslConfig sslConfig;
    private MongoConfig mongoConfig;

    public String getIsSSL() {
        return this.isSSL;
    }

    /*     */
    /*     */   private RedisConfig redisConfig;
    private MQConfig mqConfig;
    private ZookeeperConfig zookeeperConfig;

    /*     */
    public SslConfig getSslConfig() {
        /*  78 */
        return this.sslConfig;
        /*     */
    }

    public MongoConfig getMongoConfig() {
        /*  80 */
        return this.mongoConfig;
        /*     */
    }

    public RedisConfig getRedisConfig() {
        /*  82 */
        return this.redisConfig;
        /*     */
    }

    public MQConfig getMqConfig() {
        /*  84 */
        return this.mqConfig;
        /*     */
    }

    public ZookeeperConfig getZookeeperConfig() {
        /*  86 */
        return this.zookeeperConfig;
        /*     */
    }

    /*     */   private boolean pushUserStatus = true;

    /*     */
    /*     */
    public boolean isPushUserStatus() {
        /*  91 */
        return this.pushUserStatus;
        /*     */
    }

    private boolean saveChatMsg = true;

    public boolean isSaveChatMsg() {
        /*  93 */
        return this.saveChatMsg;
        /*     */
    }

    private boolean saveMucMsg = true;

    public boolean isSaveMucMsg() {
        /*  95 */
        return this.saveMucMsg;
        /*     */
    }

    /*  97 */   private String serverToken = "";

    public String getServerToken() {
        return this.serverToken;
    }

    /*     */
    /*     */   private boolean filterKeyWord = false;
    private String messageHandlerNameSpace;

    /*     */
    /*     */
    public boolean isFilterKeyWord() {
        /* 102 */
        return this.filterKeyWord;
        /*     */
    }

    /*     */
    public String getMessageHandlerNameSpace() {
        /* 105 */
        return this.messageHandlerNameSpace;
        /*     */
    }

    /*     */
    public ImConfig(Properties properties) {
        /* 108 */
        this.properties = properties;
        /*     */
    }

    /*     */
    /*     */
    public void initImConfig() {
        /*     */
        try {
            /* 113 */
            String bindIp = this.properties.getProperty("bindIp", "127.0.0.1");
            /* 114 */
            setBindIp(bindIp);
            /* 115 */
            setBindPort(Integer.valueOf(this.properties.getProperty("tcp.port", "5666")));
            /* 116 */
            WsServerConfig wsConfig = new WsServerConfig(Integer.valueOf(this.properties.getProperty("websocket.port", "5260")), true);
            /* 117 */
            setWsServerConfig(wsConfig);
            /*     */
            /* 119 */
            String wsSsl = this.properties.getProperty("websocket.ssl", "false");
            /* 120 */
            if ("true".equals(wsSsl)) {
                /* 121 */
                setWebsocketSsl(true);
                /*     */
            }
            /* 123 */
            setKeyStoreFile(this.properties.getProperty("ssl.keyStoreFile", ""));
            /*     */
            /* 125 */
            setSslPassword(this.properties.getProperty("ssl.password", ""));
            /*     */
            /* 127 */
            setCluster("true".equals(this.properties.getProperty("isCluster", "false")));
            /*     */
            /* 129 */
            this.clusterLicenseDir = this.properties.getProperty("clusterLicenseDir", "");
            /* 130 */
            this.pushUserStatus = "true".equals(this.properties.getProperty("pushUserStatus", "true"));
            /*     */
            /* 132 */
            this.serverToken = this.properties.getProperty("serverToken", "token");
            /*     */
            /* 134 */
            KConstants.isDebug = "true".equals(this.properties.getProperty("isDebug", "false"));
            /* 135 */
            System.out.println("shiku log isDebug ===> " + KConstants.isDebug);
            /* 136 */
            setMessageHandlerNameSpace(this.properties.getProperty("messageHandlerNameSpace", "com.shiku.imserver.message.hander"));
            /*     */
            /*     */
            /* 139 */
            MongoConfig mongoConfig = new MongoConfig();
            /* 140 */
            mongoConfig.setUri(this.properties.getProperty("mongoConfig.uri"));
            /*     */
            /* 142 */
            int connectTimeout = Integer.valueOf(this.properties.getProperty("mongoConfig.connectTimeout", "10000")).intValue();
            /* 143 */
            int socketTimeout = Integer.valueOf(this.properties.getProperty("mongoConfig.socketTimeout", "30000")).intValue();
            /* 144 */
            int maxWaitTime = Integer.valueOf(this.properties.getProperty("mongoConfig.maxWaitTime", "30000")).intValue();

            // 新增连接池配置读取
            int minConnectionsPerHost = Integer.valueOf(this.properties.getProperty("mongoConfig.minConnectionsPerHost", "10")).intValue();
            int maxConnectionsPerHost = Integer.valueOf(this.properties.getProperty("mongoConfig.maxConnectionsPerHost", "100")).intValue();
            int threadsAllowedToBlockForConnectionMultiplier = Integer.valueOf(this.properties.getProperty("mongoConfig.threadsAllowedToBlockForConnectionMultiplier", "5")).intValue();
            int maxConnectionIdleTime = Integer.valueOf(this.properties.getProperty("mongoConfig.maxConnectionIdleTime", "60000")).intValue();
            int maxConnectionLifeTime = Integer.valueOf(this.properties.getProperty("mongoConfig.maxConnectionLifeTime", "120000")).intValue();
            boolean socketKeepAlive = "true".equals(this.properties.getProperty("mongoConfig.socketKeepAlive", "true"));
            int heartbeatFrequency = Integer.valueOf(this.properties.getProperty("mongoConfig.heartbeatFrequency", "10000")).intValue();
            int minHeartbeatFrequency = Integer.valueOf(this.properties.getProperty("mongoConfig.minHeartbeatFrequency", "500")).intValue();
            int heartbeatConnectTimeout = Integer.valueOf(this.properties.getProperty("mongoConfig.heartbeatConnectTimeout", "20000")).intValue();
            int heartbeatSocketTimeout = Integer.valueOf(this.properties.getProperty("mongoConfig.heartbeatSocketTimeout", "20000")).intValue();

            /* 145 */
            if (0 < connectTimeout) {
                mongoConfig.setConnectTimeout(connectTimeout);
            }
            /* 147 */
            if (0 < socketTimeout)
                /* 148 */ {
                mongoConfig.setSocketTimeout(socketTimeout);
            }
            /* 149 */
            if (0 < maxWaitTime) {
                mongoConfig.setMaxWaitTime(maxWaitTime);
            }

            // 设置连接池参数
            mongoConfig.setMinConnectionsPerHost(minConnectionsPerHost);
            mongoConfig.setMaxConnectionsPerHost(maxConnectionsPerHost);
            mongoConfig.setThreadsAllowedToBlockForConnectionMultiplier(threadsAllowedToBlockForConnectionMultiplier);
            mongoConfig.setMaxConnectionIdleTime(maxConnectionIdleTime);
            mongoConfig.setMaxConnectionLifeTime(maxConnectionLifeTime);
            mongoConfig.setSocketKeepAlive(socketKeepAlive);
            mongoConfig.setHeartbeatFrequency(heartbeatFrequency);
            mongoConfig.setMinHeartbeatFrequency(minHeartbeatFrequency);
            mongoConfig.setHeartbeatConnectTimeout(heartbeatConnectTimeout);
            mongoConfig.setHeartbeatSocketTimeout(heartbeatSocketTimeout);

            /* 151 */
            setMongoConfig(mongoConfig);
            /*     */
            /* 153 */
            RedisConfig redisConfig = new RedisConfig();
            /* 154 */
            String redisUrl = this.properties.getProperty("redisConfig.address");
            /* 155 */
            String password = this.properties.getProperty("redisConfig.password");
            /*     */
            /* 157 */
            int database = Integer.valueOf(this.properties.getProperty("redisConfig.database", "0")).intValue();
            /*     */
            /* 159 */
            int pingTimeout = Integer.valueOf(this.properties.getProperty("redisConfig.pingTimeout", "0")).intValue();
            /* 160 */
            int redisTimeout = Integer.valueOf(this.properties.getProperty("redisConfig.timeout", "0")).intValue();
            /* 161 */
            int redisConnectTimeout = Integer.valueOf(this.properties.getProperty("redisConfig.connectTimeout", "0")).intValue();
            /* 162 */
            int pingConnectionInterval = Integer.valueOf(this.properties.getProperty("redisConfig.pingConnectionInterval", "0")).intValue();
            /*     */
            /* 164 */
            if (!StringUtils.isEmpty(redisUrl)) {
                /* 165 */
                redisConfig.setAddress(redisUrl);
                /*     */
            }
            /* 167 */
            redisConfig.setPassword(password);
            /* 168 */
            redisConfig.setDatabase(database);
            /* 169 */
            if (0 < pingTimeout)
                /* 170 */ {
                redisConfig.setPingTimeout(pingTimeout);
            }
            /* 171 */
            if (0 < redisTimeout)
                /* 172 */ {
                redisConfig.setTimeout(redisTimeout);
            }
            /* 173 */
            if (0 < redisConnectTimeout)
                /* 174 */ {
                redisConfig.setConnectTimeout(redisConnectTimeout);
            }
            /* 175 */
            if (0 < pingConnectionInterval) {
                /* 176 */
                redisConfig.setPingConnectionInterval(pingConnectionInterval);
                /*     */
            }
            /* 178 */
            setRedisConfig(redisConfig);
            /*     */
            /*     */
            /* 181 */
            String zk_connectStr = this.properties.getProperty("zkConfig.connectStr", "localhost:2181");
            /*     */
            /* 183 */
            int zk_sessionTimeoutMs = Integer.valueOf(this.properties.getProperty("zkConfig.sessionTimeoutMs", "10000")).intValue();
            /* 184 */
            int zk_connectionTimeoutMs = Integer.valueOf(this.properties.getProperty("zkConfig.connectionTimeoutMs", "10000")).intValue();
            /* 185 */
            String zk_namespace = this.properties.getProperty("zkConfig.namespace", "shikuim");
            /*     */
            /*     */
            /* 188 */
            ZookeeperConfig zookeeperConfig = new ZookeeperConfig(zk_connectStr, zk_sessionTimeoutMs, zk_connectionTimeoutMs, zk_namespace);
            /*     */
            /* 190 */
            setZookeeperConfig(zookeeperConfig);
            /*     */
            /* 192 */
            MQConfig mqConfig = new MQConfig();
            /* 193 */
            String mqNameAddr = this.properties.getProperty("mqConfig.nameAddr");
            /* 194 */
            int threadMin = Integer.valueOf(this.properties.getProperty("mqConfig.threadMin", "0")).intValue();
            /* 195 */
            int threadMax = Integer.valueOf(this.properties.getProperty("mqConfig.threadMax", "0")).intValue();
            /* 196 */
            int batchMaxSize = Integer.valueOf(this.properties.getProperty("mqConfig.batchMaxSize", "0")).intValue();
            /* 197 */
            if (!StringUtils.isEmpty(mqNameAddr)) {
                /* 198 */
                mqConfig.setNameAddr(mqNameAddr);
                /*     */
            }
            /* 200 */
            if (0 < threadMin)
                /* 201 */ {
                mqConfig.setThreadMin(threadMin);
            }
            /* 202 */
            if (0 < threadMax)
                /* 203 */ {
                mqConfig.setThreadMax(threadMax);
            }
            /* 204 */
            if (0 < batchMaxSize) {
                /* 205 */
                mqConfig.setBatchMaxSize(batchMaxSize);
                /*     */
            }
            /* 207 */
            setMqConfig(mqConfig);
            /* 208 */
        } catch (Exception e) {
            /* 209 */
            e.printStackTrace();
            /*     */
        }
        /*     */
    }

    /*     */   public static class MongoConfig {
        private String uri;
        private String apiUri;
        private String roomDbName;

        /* 213 */
        public void setUri(String uri) {
            this.uri = uri;
        }

        public void setApiUri(String apiUri) {
            this.apiUri = apiUri;
        }

        public void setRoomDbName(String roomDbName) {
            this.roomDbName = roomDbName;
        }

        public void setConnectTimeout(int connectTimeout) {
            this.connectTimeout = connectTimeout;
        }

        public void setSocketTimeout(int socketTimeout) {
            this.socketTimeout = socketTimeout;
        }

        public void setMaxWaitTime(int maxWaitTime) {
            this.maxWaitTime = maxWaitTime;
        }

        // 新增连接池配置参数
        private int minConnectionsPerHost = 10;
        private int maxConnectionsPerHost = 100;
        private int threadsAllowedToBlockForConnectionMultiplier = 5;
        private int maxConnectionIdleTime = 60000;
        private int maxConnectionLifeTime = 120000;
        private boolean socketKeepAlive = true;
        private int heartbeatFrequency = 10000;
        private int minHeartbeatFrequency = 500;
        private int heartbeatConnectTimeout = 20000;
        private int heartbeatSocketTimeout = 20000;

        public int getMinConnectionsPerHost() {
            return minConnectionsPerHost;
        }

        public void setMinConnectionsPerHost(int minConnectionsPerHost) {
            this.minConnectionsPerHost = minConnectionsPerHost;
        }

        public int getMaxConnectionsPerHost() {
            return maxConnectionsPerHost;
        }

        public void setMaxConnectionsPerHost(int maxConnectionsPerHost) {
            this.maxConnectionsPerHost = maxConnectionsPerHost;
        }

        public int getThreadsAllowedToBlockForConnectionMultiplier() {
            return threadsAllowedToBlockForConnectionMultiplier;
        }

        public void setThreadsAllowedToBlockForConnectionMultiplier(int threadsAllowedToBlockForConnectionMultiplier) {
            this.threadsAllowedToBlockForConnectionMultiplier = threadsAllowedToBlockForConnectionMultiplier;
        }

        public int getMaxConnectionIdleTime() {
            return maxConnectionIdleTime;
        }

        public void setMaxConnectionIdleTime(int maxConnectionIdleTime) {
            this.maxConnectionIdleTime = maxConnectionIdleTime;
        }

        public int getMaxConnectionLifeTime() {
            return maxConnectionLifeTime;
        }

        public void setMaxConnectionLifeTime(int maxConnectionLifeTime) {
            this.maxConnectionLifeTime = maxConnectionLifeTime;
        }

        public boolean isSocketKeepAlive() {
            return socketKeepAlive;
        }

        public void setSocketKeepAlive(boolean socketKeepAlive) {
            this.socketKeepAlive = socketKeepAlive;
        }

        public int getHeartbeatFrequency() {
            return heartbeatFrequency;
        }

        public void setHeartbeatFrequency(int heartbeatFrequency) {
            this.heartbeatFrequency = heartbeatFrequency;
        }

        public int getMinHeartbeatFrequency() {
            return minHeartbeatFrequency;
        }

        public void setMinHeartbeatFrequency(int minHeartbeatFrequency) {
            this.minHeartbeatFrequency = minHeartbeatFrequency;
        }

        public int getHeartbeatConnectTimeout() {
            return heartbeatConnectTimeout;
        }

        public void setHeartbeatConnectTimeout(int heartbeatConnectTimeout) {
            this.heartbeatConnectTimeout = heartbeatConnectTimeout;
        }

        public int getHeartbeatSocketTimeout() {
            return heartbeatSocketTimeout;
        }

        public void setHeartbeatSocketTimeout(int heartbeatSocketTimeout) {
            this.heartbeatSocketTimeout = heartbeatSocketTimeout;
        }

        /*     */
        /*     */
        /*     */
        /*     */
        /*     */
        /*     */
        public String getUri() {
            /* 220 */
            return this.uri;
            /*     */
        }

        public String getApiUri() {
            /* 222 */
            return this.apiUri;
            /*     */
        }

        public String getRoomDbName() {
            /* 224 */
            return this.roomDbName;
            /*     */
        }

        /* 226 */     private int connectTimeout = 20000;

        public int getConnectTimeout() {
            return this.connectTimeout;
        }

        /* 227 */      private int socketTimeout = 20000;

        public int getSocketTimeout() {
            return this.socketTimeout;
        }

        /* 228 */      private int maxWaitTime = 20000;

        public int getMaxWaitTime() {
            return this.maxWaitTime;
        }
        /*     */
    }

    /*     */
    /*     */
    /* 232 */   public static class RedisConfig {
        public void setAddress(String address) {
            this.address = address;
        }

        public void setDatabase(int database) {
            this.database = database;
        }

        public void setPassword(String password) {
            this.password = password;
        }

        public void setConnectionMinimumIdleSize(int connectionMinimumIdleSize) {
            this.connectionMinimumIdleSize = connectionMinimumIdleSize;
        }

        public void setConnectionPoolSize(int connectionPoolSize) {
            this.connectionPoolSize = connectionPoolSize;
        }

        public void setConnectTimeout(int connectTimeout) {
            this.connectTimeout = connectTimeout;
        }

        public void setPingConnectionInterval(int pingConnectionInterval) {
            this.pingConnectionInterval = pingConnectionInterval;
        }

        public void setPingTimeout(int pingTimeout) {
            this.pingTimeout = pingTimeout;
        }

        public void setTimeout(int timeout) {
            this.timeout = timeout;
        }

        public void setCluster(boolean isCluster) {
            this.isCluster = isCluster;
        }

        /*     */
        /*     */
        /* 235 */     private String address = "redis://127.0.0.1:6379";

        public String getAddress() {
            return this.address;
        }

        /* 236 */      private int database = 0;
        private String password;

        public int getDatabase() {
            return this.database;
        }

        /*     */
        public String getPassword() {
            /* 238 */
            return this.password;
            /* 239 */
        }

        private int connectionMinimumIdleSize = 32;

        public int getConnectionMinimumIdleSize() {
            return this.connectionMinimumIdleSize;
        }

        /* 240 */      private int connectionPoolSize = 64;

        public int getConnectionPoolSize() {
            return this.connectionPoolSize;
        }

        /* 241 */      private int connectTimeout = 10000;

        public int getConnectTimeout() {
            return this.connectTimeout;
        }

        /* 242 */      private int pingConnectionInterval = 500;

        public int getPingConnectionInterval() {
            return this.pingConnectionInterval;
        }

        /* 243 */      private int pingTimeout = 10000;

        public int getPingTimeout() {
            return this.pingTimeout;
        }

        /* 244 */      private int timeout = 10000;

        public int getTimeout() {
            return this.timeout;
        }

        /*     */      private boolean isCluster = false;

        public boolean isCluster() {
            /* 246 */
            return this.isCluster;
            /*     */
        }
    }

    /*     */
    /*     */   public static class MQConfig {
        /* 250 */
        public void setNameAddr(String nameAddr) {
            this.nameAddr = nameAddr;
        }

        public void setThreadMin(int threadMin) {
            this.threadMin = threadMin;
        }

        public void setThreadMax(int threadMax) {
            this.threadMax = threadMax;
        }

        public void setBatchMaxSize(int batchMaxSize) {
            this.batchMaxSize = batchMaxSize;
        }

        /*     */
        /* 252 */     protected String nameAddr = "localhost:9876";

        public String getNameAddr() {
            return this.nameAddr;
        }

        /*     */
        /* 254 */     protected int threadMin = Runtime.getRuntime().availableProcessors();

        public int getThreadMin() {
            return this.threadMin;
        }

        /*     */
        /* 256 */     protected int threadMax = Runtime.getRuntime().availableProcessors() * 2;

        public int getThreadMax() {
            return this.threadMax;
        }

        /*     */
        /* 258 */     protected int batchMaxSize = 20;

        public int getBatchMaxSize() {
            return this.batchMaxSize;
        }
        /*     */
        /*     */
    }

    /*     */
    /*     */   public static class ZookeeperConfig {
        /* 263 */
        public void setConnectStr(String connectStr) {
            this.connectStr = connectStr;
        }

        public void setSessionTimeoutMs(int sessionTimeoutMs) {
            this.sessionTimeoutMs = sessionTimeoutMs;
        }

        public void setConnectionTimeoutMs(int connectionTimeoutMs) {
            this.connectionTimeoutMs = connectionTimeoutMs;
        }

        public void setNamespace(String namespace) {
            this.namespace = namespace;
        }

        /*     */
        /*     */
        /* 266 */     protected String connectStr = "localhost:2181";

        public String getConnectStr() {
            return this.connectStr;
        }

        /*     */
        /* 268 */     protected int sessionTimeoutMs = 10000;

        public int getSessionTimeoutMs() {
            return this.sessionTimeoutMs;
        }

        /*     */
        /* 270 */     protected int connectionTimeoutMs = 10000;

        public int getConnectionTimeoutMs() {
            return this.connectionTimeoutMs;
        }

        /*     */
        /* 272 */     protected String namespace = "skim";

        public String getNamespace() {
            return this.namespace;
        }

        /*     */
        /*     */
        /*     */
        /*     */
        /*     */
        /*     */
        /*     */
        /*     */
        /*     */
        public ZookeeperConfig(String connectStr, int sessionTimeoutMs, int connectionTimeoutMs, String namespace) {
            /* 282 */
            this.connectStr = connectStr;
            /* 283 */
            this.sessionTimeoutMs = sessionTimeoutMs;
            /* 284 */
            this.connectionTimeoutMs = connectionTimeoutMs;
            /* 285 */
            this.namespace = namespace;
            /*     */
        }

        /*     */
        /*     */
        public ZookeeperConfig() {
        }
        /*     */
    }
    /*     */
}

