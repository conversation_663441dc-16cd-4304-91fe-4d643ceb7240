
package com.shiku.utils;


public class ValueUtil {

    public static Integer parse(Integer value) {

        return Integer.valueOf((null == value) ? 0 : value.intValue());

    }


    public static Long parse(Long value) {

        return Long.valueOf((null == value) ? 0L : value.longValue());

    }


    public static String parse(String value) {

        return (null == value || "".equals(value.trim())) ? "" : value;

    }

}


