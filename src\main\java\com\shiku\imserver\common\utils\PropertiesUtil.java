
package com.shiku.imserver.common.utils;


import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;


public class PropertiesUtil {

    public static Properties loadPropertyFile(String propertyFile) {

        if (StringUtils.isEmpty(propertyFile)) {

            return null;

        }

        Properties prop = new Properties();

        InputStream inputStream = null;


        try {

            if (propertyFile.startsWith("classpath:")) {

                System.out.println("loadPropertyFile ===> " + propertyFile);

                propertyFile = propertyFile.replace("classpath:", "");

                inputStream = PropertiesUtil.class.getClassLoader().getResourceAsStream(propertyFile);

            } else {

                inputStream = new FileInputStream(propertyFile);

            }

            prop.load(inputStream);

        } catch (Exception e) {

            e.printStackTrace();

            return null;

        } finally {


            try {

                if (null != inputStream) {
                    inputStream.close();
                }

            } catch (IOException e) {

                e.printStackTrace();

            }

        }

        return prop;

    }

}


