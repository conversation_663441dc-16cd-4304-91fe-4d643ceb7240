package com.shiku.imserver.common.ws;

import com.shiku.imserver.common.http.HttpRequest;
import com.shiku.imserver.common.http.HttpResponse;
import org.tio.core.ChannelContext;

public interface IWsMsgHandler {
    HttpResponse handshake(HttpRequest paramHttpRequest, HttpResponse paramHttpResponse, ChannelContext paramChannelContext) throws Exception;

    void onAfterHandshaked(HttpRequest paramHttpRequest, HttpResponse paramHttpResponse, ChannelContext paramChannelContext) throws Exception;

    Object onBytes(WsRequest paramWsRequest, byte[] paramArrayOfbyte, ChannelContext paramChannelContext) throws Exception;

    Object onClose(WsRequest paramWsRequest, byte[] paramArrayOfbyte, ChannelContext paramChannelContext) throws Exception;

    Object onText(WsRequest paramWsRequest, String paramString, ChannelContext paramChannelContext) throws Exception;
}


