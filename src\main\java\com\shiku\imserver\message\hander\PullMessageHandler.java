
package com.shiku.imserver.message.hander;

import com.shiku.imserver.CoreService;
import com.shiku.imserver.common.annotation.MessageCommandAnnotation;
import com.shiku.imserver.common.constant.KConstants;
import com.shiku.imserver.common.hander.AbstractMessageHandler;
import com.shiku.imserver.common.message.ChatMessage;
import com.shiku.imserver.common.message.PacketVO;
import com.shiku.imserver.common.message.PullBatchGroupMessage;
import com.shiku.imserver.common.message.PullMessageHistoryRecord;
import com.shiku.imserver.common.message.PullMessageHistoryRecordResp;
import com.shiku.imserver.common.packets.ImPacket;
import com.shiku.imserver.common.proto.MessageProBuf.PullBatchGroupMessageReqProBuf;
import com.shiku.imserver.common.proto.MessageProBuf.PullMessageHistoryRecordReqProBuf;
import com.shiku.imserver.common.proto.MessageProBuf.PullMessageHistoryRecordRespProBuf;
import com.shiku.imserver.common.utils.ProBufUtils;
import com.shiku.imserver.message.MessageFactory;
import com.shiku.imserver.service.IMBeanUtils;

import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.core.ChannelContext;
import org.tio.core.Tio;

public class PullMessageHandler extends AbstractMessageHandler {
    private static final Logger logger = LoggerFactory.getLogger(PullMessageHandler.class);

    public PullMessageHandler() {
    }

    @MessageCommandAnnotation(
            command = 12
    )
    public PacketVO pullMessageHandler(ImPacket packet, ChannelContext channelContext) {
        try {
            PullMessageHistoryRecord message = (PullMessageHistoryRecord) ProBufUtils.decoderMessageBody(packet.getBytes(), PullMessageHistoryRecordReqProBuf.getDescriptor(), PullMessageHistoryRecord.class);
            String from = message.getMessageHead().getFrom();
            String userId = CoreService.parseBareUserId(from);
            String jid = message.getJid();
            int size = message.getSize();
            long startTime = message.getStartTime();
            long endTime = message.getEndTime();
            if (KConstants.isDebug) {
                logger.info("pull message userId > {}  jid {} size {} startTime {} endTime {}", new Object[]{from, jid, size, startTime, endTime});
            }

            List<ChatMessage> messageList = null;
            if (1 == message.getMessageHead().getChatType()) {
                jid = jid.replace("_", "/");
                messageList = IMBeanUtils.getMessageRepository().pullChatHistoryMessage(userId, jid, size, startTime, endTime);
            } else {
                messageList = IMBeanUtils.getMessageRepository().pullGroupHistoryMessage(jid, size, startTime, endTime);
            }

            PullMessageHistoryRecordResp historyRecordResp = new PullMessageHistoryRecordResp();
            historyRecordResp.setMessageId(message.getMessageHead().getMessageId());
            historyRecordResp.setChatType(message.getMessageHead().getChatType());
            historyRecordResp.setJid(message.getJid());
            historyRecordResp.setMessageList(messageList);
            ImPacket imPacket = ProBufUtils.encodeImPacket(historyRecordResp, PullMessageHistoryRecordRespProBuf.getDescriptor());
            imPacket.setCommand((short) 13);
            Tio.send(channelContext, imPacket);
        } catch (Exception var15) {
            var15.printStackTrace();
        }

        return null;
    }

    @MessageCommandAnnotation(
            command = 14
    )
    public PacketVO pullBatchGroupMessageHandler(ImPacket packet, ChannelContext channelContext) throws Exception {
        try {
            PullBatchGroupMessage message = (PullBatchGroupMessage) ProBufUtils.decoderMessageBody(packet.getBytes(), PullBatchGroupMessageReqProBuf.getDescriptor(), PullBatchGroupMessage.class);
            ImPacket result = MessageFactory.createSuccessIMPacket(message);
            Tio.send(channelContext, result);
            IMBeanUtils.getMessageRepository().pullBatchGroupHistoryMessage(channelContext, message);
        } catch (Exception var5) {
            var5.printStackTrace();
        }

        return null;
    }
}
