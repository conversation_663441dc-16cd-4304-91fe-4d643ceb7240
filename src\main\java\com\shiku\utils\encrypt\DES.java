
package com.shiku.utils.encrypt;


import com.shiku.utils.Base64;

import java.util.Arrays;
import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;


public class DES {
    private static byte[] iv = new byte[]{1, 2, 3, 4, 5, 6, 7, 8};


    public static byte[] encrypt(byte[] encrypted, byte[] encryptKey) {

        try {

            if (encryptKey.length != 24) {

                encryptKey = Arrays.copyOfRange(encryptKey, 0, 24);

            }

            IvParameterSpec zeroIv = new IvParameterSpec(iv);

            SecretKeySpec key = new SecretKeySpec(encryptKey, "DESede");

            Cipher cipher = Cipher.getInstance("DESede/CBC/PKCS5Padding");

            cipher.init(1, key, zeroIv);


            return cipher.doFinal(encrypted);

        } catch (Exception e) {

            throw new RuntimeException(e);

        }

    }


    public static byte[] encrypt(String userId, byte[] key) {

        return encrypt(userId.getBytes(), key);

    }


    public static String encryptBase64(byte[] content, byte[] key) {

        return Base64.encode(encrypt(content, key));

    }


    public static String encryptBase64(String content, byte[] key) {

        return Base64.encode(encrypt(content.getBytes(), key));

    }


    public static byte[] decrypt(byte[] content, byte[] decryptKey) {

        try {

            if (decryptKey.length != 24) {

                decryptKey = Arrays.copyOfRange(decryptKey, 0, 24);

            }

            IvParameterSpec zeroIv = new IvParameterSpec(iv);

            SecretKeySpec key = new SecretKeySpec(decryptKey, "DESede");

            Cipher cipher = Cipher.getInstance("DESede/CBC/PKCS5Padding");

            cipher.init(2, key, zeroIv);


            return cipher.doFinal(content);

        } catch (Exception e) {

            throw new RuntimeException(e);

        }

    }


    public static String decryptString(byte[] content, byte[] key) {

        return new String(decrypt(content, key));

    }


    public static String decryptStringFromBase64(String content, byte[] key) {

        return new String(decrypt(Base64.decode(content), key));

    }

}


