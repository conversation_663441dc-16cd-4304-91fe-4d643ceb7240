
package com.shiku.imserver.repository;


public class MessageModel {
    private String body;
    private Integer direction;
    private String message;
    private Long receiver;
    private String receiver_jid;
    private Long sender;
    private String sender_jid;
    private Long ts;
    private Integer type;
    private String messageId;
    private Integer contentType;
    private String content;
    private Double timeSend;
    private long deleteTime;
    private int isRead = 0;

    private int isEncrypt = 0;


    public MessageModel() {
    }


    public MessageModel(Long sender, String sender_jid, Long receiver, String receiver_jid, Long ts, Integer direction, Integer type, String body, String message, String content) {

        this.sender = sender;

        this.sender_jid = sender_jid;

        this.receiver = receiver;

        this.receiver_jid = receiver_jid;

        this.ts = ts;

        this.direction = direction;

        this.type = type;

        this.body = body;

        this.message = message;

        this.content = content;

    }


    public String getMessageId() {

        return this.messageId;

    }


    public void setMessageId(String messageId) {

        this.messageId = messageId;

    }


    public Integer getContentType() {

        return this.contentType;

    }


    public void setContentType(Integer contentType) {

        this.contentType = contentType;

    }


    public String getContent() {

        return this.content;

    }


    public void setContent(String content) {

        this.content = content;

    }


    public String getBody() {

        return this.body;

    }


    public Integer getDirection() {

        return this.direction;

    }


    public String getMessage() {

        return this.message;

    }


    public Long getReceiver() {

        return this.receiver;

    }


    public String getReceiver_jid() {

        return this.receiver_jid;

    }


    public Long getSender() {

        return this.sender;

    }


    public String getSender_jid() {

        return this.sender_jid;

    }


    public Long getTs() {

        return this.ts;

    }


    public Integer getType() {

        return this.type;

    }


    public void setBody(String body) {

        this.body = body;

    }


    public void setDirection(Integer direction) {

        this.direction = direction;

    }


    public void setMessage(String message) {

        this.message = message;

    }


    public void setReceiver(Long receiver) {

        this.receiver = receiver;

    }


    public void setReceiver_jid(String receiver_jid) {

        this.receiver_jid = receiver_jid;

    }


    public void setSender(Long sender) {

        this.sender = sender;

    }


    public void setSender_jid(String sender_jid) {

        this.sender_jid = sender_jid;

    }


    public void setTs(Long ts) {

        this.ts = ts;

    }


    public void setType(Integer type) {

        this.type = type;

    }


    public Double getTimeSend() {

        return this.timeSend;

    }


    public void setTimeSend(Double timeSend) {

        this.timeSend = timeSend;

    }


    public long getDeleteTime() {

        return this.deleteTime;

    }


    public void setDeleteTime(long deleteTime) {

        this.deleteTime = deleteTime;

    }


    public int getIsRead() {

        return this.isRead;

    }


    public void setIsRead(int isRead) {

        this.isRead = isRead;

    }


    public int getIsEncrypt() {

        return this.isEncrypt;

    }


    public void setIsEncrypt(int isEncrypt) {

        this.isEncrypt = isEncrypt;

    }

}


