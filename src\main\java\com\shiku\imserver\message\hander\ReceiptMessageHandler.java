

package com.shiku.imserver.message.hander;

import com.shiku.imserver.common.annotation.MessageCommandAnnotation;
import com.shiku.imserver.common.hander.AbstractMessageHandler;
import com.shiku.imserver.common.message.MessageReceiptStatus;
import com.shiku.imserver.common.message.PacketVO;
import com.shiku.imserver.common.packets.ImPacket;
import com.shiku.imserver.common.proto.MessageProBuf.MessageReceiptStatusProBuf;
import com.shiku.imserver.common.utils.ProBufUtils;
import com.shiku.imserver.service.IMBeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.core.ChannelContext;

public class ReceiptMessageHandler extends AbstractMessageHandler {
    private static final Logger logger = LoggerFactory.getLogger(ReceiptMessageHandler.class);

    public ReceiptMessageHandler() {
    }

    @MessageCommandAnnotation(
            command = 11
    )
    public PacketVO receiptMessageHandler(ImPacket packet, ChannelContext channelContext) {
        try {
            MessageReceiptStatus message = (MessageReceiptStatus) ProBufUtils.decoderMessageBody(packet.getBytes(), MessageReceiptStatusProBuf.getDescriptor(), MessageReceiptStatus.class);
            byte chatType = message.getMessageHead().getChatType();
            String from = message.getMessageHead().getFrom();
            String to = message.getMessageHead().getTo();
            if (chatType == 1) {
                IMBeanUtils.getReceiptLogicService().removeMessage(channelContext, message.getMessageId(), (String) null);
            } else if (chatType == 2) {
                IMBeanUtils.getReceiptLogicService().removeMessage(channelContext, message.getMessageId(), to);
            }
        } catch (Exception var7) {
            var7.printStackTrace();
        }

        return null;
    }
}
