package com.shiku.imserver.common.packets;

public interface Command {
    public static final short COMMAND_UNKNOW = 0;

    public static final short COMMAND_HANDSHAKE_REQ = 1;

    public static final short COMMAND_HANDSHAKE_RESP = 2;

    public static final short COMMAND_AUTH_REQ = 5;

    public static final short COMMAND_AUTH_RESP = 6;

    public static final short COMMAND_CLOSE = 7;

    public static final short COMMAND_CHAT = 10;

    public static final short MESSAGE_RECEIPT = 11;

    public static final short PULL_MESSAGE_RECORD_REQ = 12;

    public static final short PULL_MESSAGE_RECORD_RESP = 13;

    public static final short PULL_BATCH_GROUP_MESSAGE_REQ = 14;

    public static final short PULL_BATCH_GROUP_MESSAGE_RESP = 15;

    public static final short ERROR = -1;

    public static final short Login_Conflict = -3;

    public static final short JOINGROUP_REQ = 20;

    public static final short EXITGROUP_REQ = 21;

    public static final short GROUP_REQUEST_RESULT = 22;

    public static final short Ping_REQ = 99;

    public static final short SUCCESS = 100;

    public static final short SERVER_REQ = 200;
}


