<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AnalysisUIOptions">
    <option name="GROUP_BY_SEVERITY" value="true" />
  </component>
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="c8b2eb86-7daf-49eb-aa76-7f230996ea77" name="Default Changelist" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="CompilerWorkspaceConfiguration">
    <option name="MAKE_PROJECT_ON_SAVE" value="true" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="JRebelWorkspace">
    <option name="jrebelEnabledAutocompile" value="true" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="mavenHome" value="$USER_HOME$/.m2/wrapper/dists/apache-maven-3.6.3-bin/1iopthnavndlasol9gbrbg6bf2/apache-maven-3.6.3" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectId" id="1haWaYYNmvHW7Zo4PTZpSr64IpF" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="RequestMappingsPanelOrder0" value="0" />
    <property name="RequestMappingsPanelOrder1" value="1" />
    <property name="RequestMappingsPanelWidth0" value="75" />
    <property name="RequestMappingsPanelWidth1" value="75" />
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="WebServerToolWindowFactoryState" value="false" />
    <property name="aspect.path.notification.shown" value="true" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$/src/main/java/com/shiku/commons" />
    <property name="node.js.detected.package.eslint" value="true" />
    <property name="node.js.detected.package.tslint" value="true" />
    <property name="node.js.path.for.package.eslint" value="project" />
    <property name="node.js.path.for.package.tslint" value="project" />
    <property name="node.js.selected.package.eslint" value="(autodetect)" />
    <property name="node.js.selected.package.tslint" value="(autodetect)" />
    <property name="nodejs_package_manager_path" value="npm" />
    <property name="project.structure.last.edited" value="Libraries" />
    <property name="project.structure.proportion" value="0.15" />
    <property name="project.structure.side.proportion" value="0.5045977" />
  </component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RebelAgentSelection">
    <selection>jr</selection>
  </component>
  <component name="RecentsManager">
    <key name="CopyClassDialog.RECENTS_KEY">
      <recent name="com.shiku" />
    </key>
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/src/main/java/com/shiku/commons" />
      <recent name="$PROJECT_DIR$/src/main/java/com/shiku" />
    </key>
  </component>
  <component name="RunAnythingCache">
    <option name="myCommands">
      <command value="mvn install" />
      <command value="mvn clean" />
    </option>
  </component>
  <component name="RunManager">
    <configuration name="IMServerStarterApplication" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.shiku.imserver.IMServerStarterApplication" />
      <module name="shiku" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.shiku.imserver.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Application.IMServerStarterApplication" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="c8b2eb86-7daf-49eb-aa76-7f230996ea77" name="Default Changelist" comment="" />
      <created>1600250113462</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1600250113462</updated>
      <workItem from="1600250115205" duration="1391000" />
      <workItem from="1600304802321" duration="823000" />
      <workItem from="1600305629781" duration="1308000" />
      <workItem from="1600306954369" duration="145000" />
      <workItem from="1600307102169" duration="1557000" />
      <workItem from="1600308703763" duration="182000" />
      <workItem from="1600308889871" duration="1650000" />
      <workItem from="1600337021383" duration="1399000" />
      <workItem from="1600345270967" duration="395000" />
      <workItem from="1600346604680" duration="135000" />
      <workItem from="1600346821017" duration="670000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="WindowStateProjectService">
    <state x="448" y="121" key="#Project_Structure" timestamp="1600308907007">
      <screen x="0" y="0" width="1920" height="1080" />
    </state>
    <state x="448" y="121" key="#Project_Structure/0.0.1920.1080@0.0.1920.1080" timestamp="1600308907007" />
    <state x="429" y="277" width="1146" height="724" key="#com.intellij.execution.impl.EditConfigurationsDialog" timestamp="1600310806086">
      <screen x="0" y="23" width="1920" height="978" />
    </state>
    <state x="429" y="277" width="1146" height="724" key="#com.intellij.execution.impl.EditConfigurationsDialog/0.23.1920.978@0.23.1920.978" timestamp="1600310806086" />
    <state x="615" y="193" key="#com.intellij.openapi.keymap.impl.ui.EditKeymapsDialog" timestamp="1600306118711">
      <screen x="0" y="23" width="1920" height="977" />
    </state>
    <state x="615" y="193" key="#com.intellij.openapi.keymap.impl.ui.EditKeymapsDialog/0.23.1920.977@0.23.1920.977" timestamp="1600306118711" />
    <state x="510" y="0" key="#com.intellij.refactoring.rename.AutomaticRenamingDialog" timestamp="1600310711702">
      <screen x="0" y="0" width="1920" height="1080" />
    </state>
    <state x="510" y="0" key="#com.intellij.refactoring.rename.AutomaticRenamingDialog/0.0.1920.1080@0.0.1920.1080" timestamp="1600310711702" />
    <state width="1878" height="264" key="GridCell.Tab.0.bottom" timestamp="1600347151437">
      <screen x="0" y="23" width="1920" height="988" />
    </state>
    <state width="1878" height="258" key="GridCell.Tab.0.bottom/0.0.1920.1080@0.0.1920.1080" timestamp="1600345747315" />
    <state width="1878" height="263" key="GridCell.Tab.0.bottom/0.23.1920.977@0.23.1920.977" timestamp="1600310446906" />
    <state width="1878" height="348" key="GridCell.Tab.0.bottom/0.23.1920.978@0.23.1920.978" timestamp="1600311206149" />
    <state width="1878" height="348" key="GridCell.Tab.0.bottom/0.23.1920.980@0.23.1920.980" timestamp="1600338985182" />
    <state width="1878" height="342" key="GridCell.Tab.0.bottom/0.23.1920.982@0.23.1920.982" timestamp="1600345477216" />
    <state width="1878" height="264" key="GridCell.Tab.0.bottom/0.23.1920.986@0.23.1920.986" timestamp="1600346846014" />
    <state width="1878" height="264" key="GridCell.Tab.0.bottom/0.23.1920.988@0.23.1920.988" timestamp="1600347151437" />
    <state width="1878" height="264" key="GridCell.Tab.0.center" timestamp="1600347151436">
      <screen x="0" y="23" width="1920" height="988" />
    </state>
    <state width="1878" height="258" key="GridCell.Tab.0.center/0.0.1920.1080@0.0.1920.1080" timestamp="1600345747315" />
    <state width="1878" height="263" key="GridCell.Tab.0.center/0.23.1920.977@0.23.1920.977" timestamp="1600310446905" />
    <state width="1878" height="348" key="GridCell.Tab.0.center/0.23.1920.978@0.23.1920.978" timestamp="1600311206149" />
    <state width="1878" height="348" key="GridCell.Tab.0.center/0.23.1920.980@0.23.1920.980" timestamp="1600338985181" />
    <state width="1878" height="342" key="GridCell.Tab.0.center/0.23.1920.982@0.23.1920.982" timestamp="1600345477215" />
    <state width="1878" height="264" key="GridCell.Tab.0.center/0.23.1920.986@0.23.1920.986" timestamp="1600346846014" />
    <state width="1878" height="264" key="GridCell.Tab.0.center/0.23.1920.988@0.23.1920.988" timestamp="1600347151436" />
    <state width="1878" height="264" key="GridCell.Tab.0.left" timestamp="1600347151436">
      <screen x="0" y="23" width="1920" height="988" />
    </state>
    <state width="1878" height="258" key="GridCell.Tab.0.left/0.0.1920.1080@0.0.1920.1080" timestamp="1600345747314" />
    <state width="1878" height="263" key="GridCell.Tab.0.left/0.23.1920.977@0.23.1920.977" timestamp="1600310446905" />
    <state width="1878" height="348" key="GridCell.Tab.0.left/0.23.1920.978@0.23.1920.978" timestamp="1600311206148" />
    <state width="1878" height="348" key="GridCell.Tab.0.left/0.23.1920.980@0.23.1920.980" timestamp="1600338985181" />
    <state width="1878" height="342" key="GridCell.Tab.0.left/0.23.1920.982@0.23.1920.982" timestamp="1600345477215" />
    <state width="1878" height="264" key="GridCell.Tab.0.left/0.23.1920.986@0.23.1920.986" timestamp="1600346846013" />
    <state width="1878" height="264" key="GridCell.Tab.0.left/0.23.1920.988@0.23.1920.988" timestamp="1600347151436" />
    <state width="1878" height="264" key="GridCell.Tab.0.right" timestamp="1600347151437">
      <screen x="0" y="23" width="1920" height="988" />
    </state>
    <state width="1878" height="258" key="GridCell.Tab.0.right/0.0.1920.1080@0.0.1920.1080" timestamp="1600345747315" />
    <state width="1878" height="263" key="GridCell.Tab.0.right/0.23.1920.977@0.23.1920.977" timestamp="1600310446905" />
    <state width="1878" height="348" key="GridCell.Tab.0.right/0.23.1920.978@0.23.1920.978" timestamp="1600311206149" />
    <state width="1878" height="348" key="GridCell.Tab.0.right/0.23.1920.980@0.23.1920.980" timestamp="1600338985181" />
    <state width="1878" height="342" key="GridCell.Tab.0.right/0.23.1920.982@0.23.1920.982" timestamp="1600345477216" />
    <state width="1878" height="264" key="GridCell.Tab.0.right/0.23.1920.986@0.23.1920.986" timestamp="1600346846014" />
    <state width="1878" height="264" key="GridCell.Tab.0.right/0.23.1920.988@0.23.1920.988" timestamp="1600347151437" />
    <state width="1878" height="342" key="GridCell.Tab.1.bottom" timestamp="1600345747309">
      <screen x="0" y="0" width="1920" height="1080" />
    </state>
    <state width="1878" height="342" key="GridCell.Tab.1.bottom/0.0.1920.1080@0.0.1920.1080" timestamp="1600345747309" />
    <state width="1878" height="348" key="GridCell.Tab.1.bottom/0.23.1920.977@0.23.1920.977" timestamp="1600310444013" />
    <state width="1878" height="348" key="GridCell.Tab.1.bottom/0.23.1920.978@0.23.1920.978" timestamp="1600311206040" />
    <state width="1878" height="348" key="GridCell.Tab.1.bottom/0.23.1920.980@0.23.1920.980" timestamp="1600338985182" />
    <state width="1878" height="342" key="GridCell.Tab.1.bottom/0.23.1920.982@0.23.1920.982" timestamp="1600345477217" />
    <state width="1878" height="342" key="GridCell.Tab.1.center" timestamp="1600345747309">
      <screen x="0" y="0" width="1920" height="1080" />
    </state>
    <state width="1878" height="342" key="GridCell.Tab.1.center/0.0.1920.1080@0.0.1920.1080" timestamp="1600345747309" />
    <state width="1878" height="348" key="GridCell.Tab.1.center/0.23.1920.977@0.23.1920.977" timestamp="1600310444013" />
    <state width="1878" height="348" key="GridCell.Tab.1.center/0.23.1920.978@0.23.1920.978" timestamp="1600311206040" />
    <state width="1878" height="348" key="GridCell.Tab.1.center/0.23.1920.980@0.23.1920.980" timestamp="1600338985182" />
    <state width="1878" height="342" key="GridCell.Tab.1.center/0.23.1920.982@0.23.1920.982" timestamp="1600345477216" />
    <state width="1878" height="342" key="GridCell.Tab.1.left" timestamp="1600345747309">
      <screen x="0" y="0" width="1920" height="1080" />
    </state>
    <state width="1878" height="342" key="GridCell.Tab.1.left/0.0.1920.1080@0.0.1920.1080" timestamp="1600345747309" />
    <state width="1878" height="348" key="GridCell.Tab.1.left/0.23.1920.977@0.23.1920.977" timestamp="1600310444013" />
    <state width="1878" height="348" key="GridCell.Tab.1.left/0.23.1920.978@0.23.1920.978" timestamp="1600311206040" />
    <state width="1878" height="348" key="GridCell.Tab.1.left/0.23.1920.980@0.23.1920.980" timestamp="1600338985182" />
    <state width="1878" height="342" key="GridCell.Tab.1.left/0.23.1920.982@0.23.1920.982" timestamp="1600345477216" />
    <state width="1878" height="342" key="GridCell.Tab.1.right" timestamp="1600345747309">
      <screen x="0" y="0" width="1920" height="1080" />
    </state>
    <state width="1878" height="342" key="GridCell.Tab.1.right/0.0.1920.1080@0.0.1920.1080" timestamp="1600345747309" />
    <state width="1878" height="348" key="GridCell.Tab.1.right/0.23.1920.977@0.23.1920.977" timestamp="1600310444013" />
    <state width="1878" height="348" key="GridCell.Tab.1.right/0.23.1920.978@0.23.1920.978" timestamp="1600311206040" />
    <state width="1878" height="348" key="GridCell.Tab.1.right/0.23.1920.980@0.23.1920.980" timestamp="1600338985182" />
    <state width="1878" height="342" key="GridCell.Tab.1.right/0.23.1920.982@0.23.1920.982" timestamp="1600345477216" />
    <state width="1878" height="348" key="GridCell.Tab.2.bottom" timestamp="1600311206041">
      <screen x="0" y="23" width="1920" height="978" />
    </state>
    <state width="1878" height="348" key="GridCell.Tab.2.bottom/0.23.1920.977@0.23.1920.977" timestamp="1600310444014" />
    <state width="1878" height="348" key="GridCell.Tab.2.bottom/0.23.1920.978@0.23.1920.978" timestamp="1600311206041" />
    <state width="1878" height="348" key="GridCell.Tab.2.center" timestamp="1600311206041">
      <screen x="0" y="23" width="1920" height="978" />
    </state>
    <state width="1878" height="348" key="GridCell.Tab.2.center/0.23.1920.977@0.23.1920.977" timestamp="1600310444013" />
    <state width="1878" height="348" key="GridCell.Tab.2.center/0.23.1920.978@0.23.1920.978" timestamp="1600311206041" />
    <state width="1878" height="348" key="GridCell.Tab.2.left" timestamp="1600311206041">
      <screen x="0" y="23" width="1920" height="978" />
    </state>
    <state width="1878" height="348" key="GridCell.Tab.2.left/0.23.1920.977@0.23.1920.977" timestamp="1600310444013" />
    <state width="1878" height="348" key="GridCell.Tab.2.left/0.23.1920.978@0.23.1920.978" timestamp="1600311206041" />
    <state width="1878" height="348" key="GridCell.Tab.2.right" timestamp="1600311206041">
      <screen x="0" y="23" width="1920" height="978" />
    </state>
    <state width="1878" height="348" key="GridCell.Tab.2.right/0.23.1920.977@0.23.1920.977" timestamp="1600310444014" />
    <state width="1878" height="348" key="GridCell.Tab.2.right/0.23.1920.978@0.23.1920.978" timestamp="1600311206041" />
    <state x="448" y="93" key="SettingsEditor" timestamp="1600338983496">
      <screen x="0" y="23" width="1920" height="980" />
    </state>
    <state x="448" y="77" key="SettingsEditor/0.0.1920.1080@0.0.1920.1080" timestamp="1600309002326" />
    <state x="448" y="93" key="SettingsEditor/0.23.1920.977@0.23.1920.977" timestamp="1600308603155" />
    <state x="448" y="93" key="SettingsEditor/0.23.1920.980@0.23.1920.980" timestamp="1600338983496" />
    <state x="667" y="100" width="586" height="823" key="find.popup" timestamp="1600311240408">
      <screen x="0" y="23" width="1920" height="978" />
    </state>
    <state x="667" y="100" width="586" height="822" key="find.popup/0.23.1920.977@0.23.1920.977" timestamp="1600307183297" />
    <state x="667" y="100" width="586" height="823" key="find.popup/0.23.1920.978@0.23.1920.978" timestamp="1600311240408" />
    <state x="609" y="234" width="700" height="530" key="recent.locations.popup" timestamp="1600306351518">
      <screen x="0" y="23" width="1920" height="977" />
    </state>
    <state x="609" y="234" width="700" height="530" key="recent.locations.popup/0.23.1920.977@0.23.1920.977" timestamp="1600306351518" />
    <state x="625" y="236" width="670" height="678" key="run.anything.popup" timestamp="1600337136592">
      <screen x="0" y="23" width="1920" height="980" />
    </state>
    <state x="625" y="235" width="670" height="747" key="run.anything.popup/0.0.1920.1080@0.0.1920.1080" timestamp="1600310961634" />
    <state x="625" y="236" width="670" height="675" key="run.anything.popup/0.23.1920.977@0.23.1920.977" timestamp="1600308005577" />
    <state x="625" y="236" width="670" height="678" key="run.anything.popup/0.23.1920.980@0.23.1920.980" timestamp="1600337136592" />
    <state x="630" y="325" width="670" height="676" key="search.everywhere.popup" timestamp="1600310844528">
      <screen x="0" y="23" width="1920" height="978" />
    </state>
    <state x="630" y="325" width="670" height="676" key="search.everywhere.popup/0.23.1920.978@0.23.1920.978" timestamp="1600310844528" />
  </component>
</project>