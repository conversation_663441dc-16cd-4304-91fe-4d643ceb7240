# ========== 基础服务配置 ==========
# 服务器绑定IP和端口
bindIp=0.0.0.0
tcp.port=5666
websocket.port=5280

# SSL配置
websocket.ssl=false
ssl.keyStoreFile=classpath:socket.jks
ssl.password=123456

# 调试模式
isDebug=false

# 服务器令牌
serverToken=shiku_server_token_2024

# 消息处理器包路径
messageHandlerNameSpace=com.shiku.imserver.message.hander

# ========== 集群配置 ==========
# 启用集群模式
isCluster=true
# 集群许可证目录
clusterLicenseDir=/opt/shiku/license
# 推送用户状态
pushUserStatus=true

# ========== MongoDB配置 ==========
# MongoDB连接URI
mongoConfig.uri=**************************************************************************************************************
# API专用MongoDB URI（可选）
mongoConfig.apiUri=***************************************************************************************************************
# 连接超时（毫秒）
mongoConfig.connectTimeout=10000
# Socket超时（毫秒）
mongoConfig.socketTimeout=20000
# 最大等待时间（毫秒）
mongoConfig.maxWaitTime=12000000

# ========== Redis配置 ==========
# Redis集群地址
redisConfig.address=redis://redis-cluster-1:6379,redis://redis-cluster-2:6379,redis://redis-cluster-3:6379
# Redis密码
redisConfig.password=redis_password_2024
# 数据库索引
redisConfig.database=0
# Ping超时（毫秒）
redisConfig.pingTimeout=3000
# 连接超时（毫秒）
redisConfig.timeout=10000
redisConfig.connectTimeout=10000
# 连接池配置
redisConfig.connectionMinimumIdleSize=32
redisConfig.connectionPoolSize=64
redisConfig.subscriptionConnectionMinimumIdleSize=1
redisConfig.subscriptionConnectionPoolSize=50

# ========== Zookeeper配置 ==========
# Zookeeper集群地址
zkConfig.connectStr=zk1:2181,zk2:2181,zk3:2181
# 会话超时（毫秒）
zkConfig.sessionTimeoutMs=30000
# 连接超时（毫秒）
zkConfig.connectionTimeoutMs=15000
# 命名空间
zkConfig.namespace=shiku_im_cluster

# ========== RocketMQ配置 ==========
# NameServer地址
mqConfig.nameAddr=rocketmq-nameserver1:9876;rocketmq-nameserver2:9876
# 线程池配置
mqConfig.threadMin=4
mqConfig.threadMax=16
# 批量消息大小
mqConfig.batchMaxSize=1000