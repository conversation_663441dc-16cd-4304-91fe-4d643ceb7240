#!/bin/bash

echo "========== Shiku IM 集群管理脚本 =========="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

COMPOSE_FILE="docker-compose-cluster.yml"

# 显示菜单
show_menu() {
    echo ""
    echo "========== 集群管理菜单 =========="
    echo "1. 启动整个集群"
    echo "2. 停止整个集群"
    echo "3. 重启整个集群"
    echo "4. 扩容IM服务器节点"
    echo "5. 缩容IM服务器节点"
    echo "6. 更新IM服务器"
    echo "7. 备份数据"
    echo "8. 恢复数据"
    echo "9. 查看集群状态"
    echo "10. 查看服务日志"
    echo "11. 清理无用资源"
    echo "0. 退出"
    echo "=========================="
}

# 启动集群
start_cluster() {
    echo -e "${GREEN}启动Shiku IM集群...${NC}"
    
    # 按顺序启动服务
    echo "1. 启动Zookeeper集群..."
    docker-compose -f $COMPOSE_FILE up -d zk1 zk2 zk3
    sleep 20
    
    echo "2. 启动MongoDB副本集..."
    docker-compose -f $COMPOSE_FILE up -d mongodb1 mongodb2 mongodb3
    sleep 30
    
    echo "3. 启动Redis集群..."
    docker-compose -f $COMPOSE_FILE up -d redis-cluster-1 redis-cluster-2 redis-cluster-3
    sleep 20
    
    echo "4. 启动RocketMQ..."
    docker-compose -f $COMPOSE_FILE up -d rocketmq-nameserver1 rocketmq-nameserver2
    sleep 20
    docker-compose -f $COMPOSE_FILE up -d rocketmq-broker
    sleep 20
    
    echo "5. 启动IM服务器集群..."
    docker-compose -f $COMPOSE_FILE up -d shiku-im-1 shiku-im-2 shiku-im-3
    sleep 30
    
    echo "6. 启动负载均衡器..."
    docker-compose -f $COMPOSE_FILE up -d nginx
    
    echo -e "${GREEN}集群启动完成！${NC}"
}

# 停止集群
stop_cluster() {
    echo -e "${YELLOW}停止Shiku IM集群...${NC}"
    docker-compose -f $COMPOSE_FILE down
    echo -e "${GREEN}集群已停止${NC}"
}

# 重启集群
restart_cluster() {
    echo -e "${YELLOW}重启Shiku IM集群...${NC}"
    stop_cluster
    sleep 10
    start_cluster
}

# 扩容IM服务器节点
scale_up() {
    read -p "请输入要扩容到的节点数量: " node_count
    
    if ! [[ "$node_count" =~ ^[0-9]+$ ]] || [ "$node_count" -lt 1 ]; then
        echo -e "${RED}错误: 请输入有效的节点数量${NC}"
        return 1
    fi
    
    echo -e "${GREEN}扩容IM服务器到 $node_count 个节点...${NC}"
    
    # 这里需要动态生成新的服务配置
    # 简化版本：只支持预定义的3个节点
    if [ "$node_count" -gt 3 ]; then
        echo -e "${YELLOW}当前配置最多支持3个节点，启动所有节点...${NC}"
        docker-compose -f $COMPOSE_FILE up -d shiku-im-1 shiku-im-2 shiku-im-3
    else
        for i in $(seq 1 $node_count); do
            docker-compose -f $COMPOSE_FILE up -d shiku-im-$i
        done
    fi
    
    # 更新负载均衡器配置
    docker-compose -f $COMPOSE_FILE restart nginx
    
    echo -e "${GREEN}扩容完成${NC}"
}

# 缩容IM服务器节点
scale_down() {
    read -p "请输入要保留的节点数量: " node_count
    
    if ! [[ "$node_count" =~ ^[0-9]+$ ]] || [ "$node_count" -lt 1 ]; then
        echo -e "${RED}错误: 请输入有效的节点数量${NC}"
        return 1
    fi
    
    echo -e "${YELLOW}缩容IM服务器到 $node_count 个节点...${NC}"
    
    # 停止多余的节点
    for i in $(seq $((node_count + 1)) 3); do
        docker-compose -f $COMPOSE_FILE stop shiku-im-$i
        docker-compose -f $COMPOSE_FILE rm -f shiku-im-$i
    done
    
    # 更新负载均衡器配置
    docker-compose -f $COMPOSE_FILE restart nginx
    
    echo -e "${GREEN}缩容完成${NC}"
}

# 更新IM服务器
update_im_servers() {
    echo -e "${GREEN}更新IM服务器...${NC}"
    
    # 构建新镜像
    echo "1. 构建新的应用镜像..."
    mvn clean package -DskipTests
    docker-compose -f $COMPOSE_FILE build shiku-im-1
    
    # 滚动更新
    echo "2. 执行滚动更新..."
    for service in shiku-im-1 shiku-im-2 shiku-im-3; do
        echo "更新 $service..."
        docker-compose -f $COMPOSE_FILE stop $service
        docker-compose -f $COMPOSE_FILE up -d $service
        
        # 等待服务健康检查
        echo "等待 $service 启动完成..."
        sleep 30
        
        # 简单的健康检查
        if docker-compose -f $COMPOSE_FILE ps $service | grep -q "Up"; then
            echo -e "${GREEN}$service 更新成功${NC}"
        else
            echo -e "${RED}$service 更新失败${NC}"
            return 1
        fi
    done
    
    echo -e "${GREEN}所有IM服务器更新完成${NC}"
}

# 备份数据
backup_data() {
    local backup_dir="./backups/$(date +%Y%m%d_%H%M%S)"
    mkdir -p $backup_dir
    
    echo -e "${GREEN}开始备份数据到 $backup_dir...${NC}"
    
    # 备份MongoDB
    echo "备份MongoDB数据..."
    docker exec shiku_mongodb1_1 mongodump --host mongodb1:27017 --out /tmp/backup
    docker cp shiku_mongodb1_1:/tmp/backup $backup_dir/mongodb
    
    # 备份Redis
    echo "备份Redis数据..."
    docker exec shiku_redis-cluster-1_1 redis-cli --rdb /tmp/dump.rdb
    docker cp shiku_redis-cluster-1_1:/tmp/dump.rdb $backup_dir/redis_dump.rdb
    
    # 备份配置文件
    echo "备份配置文件..."
    cp -r src/main/resources $backup_dir/config
    
    echo -e "${GREEN}数据备份完成: $backup_dir${NC}"
}

# 恢复数据
restore_data() {
    echo "可用的备份:"
    ls -la ./backups/ 2>/dev/null || echo "没有找到备份目录"
    
    read -p "请输入要恢复的备份目录名称: " backup_name
    local backup_dir="./backups/$backup_name"
    
    if [ ! -d "$backup_dir" ]; then
        echo -e "${RED}错误: 备份目录不存在${NC}"
        return 1
    fi
    
    echo -e "${YELLOW}从 $backup_dir 恢复数据...${NC}"
    
    # 恢复MongoDB
    if [ -d "$backup_dir/mongodb" ]; then
        echo "恢复MongoDB数据..."
        docker cp $backup_dir/mongodb shiku_mongodb1_1:/tmp/restore
        docker exec shiku_mongodb1_1 mongorestore --host mongodb1:27017 /tmp/restore
    fi
    
    # 恢复Redis
    if [ -f "$backup_dir/redis_dump.rdb" ]; then
        echo "恢复Redis数据..."
        docker cp $backup_dir/redis_dump.rdb shiku_redis-cluster-1_1:/tmp/dump.rdb
        docker-compose -f $COMPOSE_FILE restart redis-cluster-1
    fi
    
    echo -e "${GREEN}数据恢复完成${NC}"
}

# 查看集群状态
show_cluster_status() {
    echo -e "${BLUE}========== 集群状态 ==========${NC}"
    docker-compose -f $COMPOSE_FILE ps
    
    echo ""
    echo -e "${BLUE}========== 资源使用情况 ==========${NC}"
    docker stats --no-stream --format "table {{.Name}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}"
}

# 查看服务日志
show_service_logs() {
    echo "可用的服务:"
    docker-compose -f $COMPOSE_FILE config --services
    echo ""
    read -p "请输入服务名称: " service_name
    
    if [ -n "$service_name" ]; then
        read -p "显示最近多少行日志 (默认100): " lines
        lines=${lines:-100}
        docker-compose -f $COMPOSE_FILE logs --tail=$lines -f $service_name
    fi
}

# 清理无用资源
cleanup_resources() {
    echo -e "${YELLOW}清理无用的Docker资源...${NC}"
    
    read -p "是否清理停止的容器? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker container prune -f
    fi
    
    read -p "是否清理无用的镜像? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker image prune -f
    fi
    
    read -p "是否清理无用的网络? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker network prune -f
    fi
    
    read -p "是否清理无用的数据卷? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker volume prune -f
    fi
    
    echo -e "${GREEN}清理完成${NC}"
}

# 主程序循环
while true; do
    show_menu
    read -p "请选择操作 (0-11): " choice
    
    case $choice in
        1) start_cluster ;;
        2) stop_cluster ;;
        3) restart_cluster ;;
        4) scale_up ;;
        5) scale_down ;;
        6) update_im_servers ;;
        7) backup_data ;;
        8) restore_data ;;
        9) show_cluster_status ;;
        10) show_service_logs ;;
        11) cleanup_resources ;;
        0) echo "退出管理脚本"; exit 0 ;;
        *) echo -e "${RED}无效选择，请重新输入${NC}" ;;
    esac
    
    echo ""
    read -p "按回车键继续..."
done
