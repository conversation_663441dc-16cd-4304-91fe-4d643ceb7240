version: '3.8'

services:
  # ========== Zookeeper集群 ==========
  zk1:
    image: zookeeper:3.8
    hostname: zk1
    ports:
      - "2181:2181"
    environment:
      ZOO_MY_ID: 1
      ZOO_SERVERS: server.1=zk1:2888:3888;2181 server.2=zk2:2888:3888;2181 server.3=zk3:2888:3888;2181
    volumes:
      - zk1_data:/data
      - zk1_logs:/logs
    networks:
      - shiku-cluster

  zk2:
    image: zookeeper:3.8
    hostname: zk2
    ports:
      - "2182:2181"
    environment:
      ZOO_MY_ID: 2
      ZOO_SERVERS: server.1=zk1:2888:3888;2181 server.2=zk2:2888:3888;2181 server.3=zk3:2888:3888;2181
    volumes:
      - zk2_data:/data
      - zk2_logs:/logs
    networks:
      - shiku-cluster

  zk3:
    image: zookeeper:3.8
    hostname: zk3
    ports:
      - "2183:2181"
    environment:
      ZOO_MY_ID: 3
      ZOO_SERVERS: server.1=zk1:2888:3888;2181 server.2=zk2:2888:3888;2181 server.3=zk3:2888:3888;2181
    volumes:
      - zk3_data:/data
      - zk3_logs:/logs
    networks:
      - shiku-cluster

  # ========== MongoDB副本集 ==========
  mongodb1:
    image: mongo:4.4
    hostname: mongodb1
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
    command: mongod --replSet rs0 --bind_ip_all
    volumes:
      - mongodb1_data:/data/db
    networks:
      - shiku-cluster

  mongodb2:
    image: mongo:4.4
    hostname: mongodb2
    ports:
      - "27018:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
    command: mongod --replSet rs0 --bind_ip_all
    volumes:
      - mongodb2_data:/data/db
    networks:
      - shiku-cluster

  mongodb3:
    image: mongo:4.4
    hostname: mongodb3
    ports:
      - "27019:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: admin
      MONGO_INITDB_ROOT_PASSWORD: password
    command: mongod --replSet rs0 --bind_ip_all
    volumes:
      - mongodb3_data:/data/db
    networks:
      - shiku-cluster

  # ========== Redis集群 ==========
  redis-cluster-1:
    image: redis:7-alpine
    hostname: redis-cluster-1
    ports:
      - "6379:6379"
    command: redis-server --requirepass redis_password_2024 --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes
    volumes:
      - redis1_data:/data
    networks:
      - shiku-cluster

  redis-cluster-2:
    image: redis:7-alpine
    hostname: redis-cluster-2
    ports:
      - "6380:6379"
    command: redis-server --requirepass redis_password_2024 --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes
    volumes:
      - redis2_data:/data
    networks:
      - shiku-cluster

  redis-cluster-3:
    image: redis:7-alpine
    hostname: redis-cluster-3
    ports:
      - "6381:6379"
    command: redis-server --requirepass redis_password_2024 --cluster-enabled yes --cluster-config-file nodes.conf --cluster-node-timeout 5000 --appendonly yes
    volumes:
      - redis3_data:/data
    networks:
      - shiku-cluster

  # ========== RocketMQ ==========
  rocketmq-nameserver1:
    image: apache/rocketmq:4.9.4
    hostname: rocketmq-nameserver1
    ports:
      - "9876:9876"
    environment:
      JAVA_OPT: "-Duser.home=/opt -Xms512m -Xmx512m"
    command: sh mqnamesrv
    volumes:
      - rocketmq_nameserver1_logs:/opt/logs
    networks:
      - shiku-cluster

  rocketmq-nameserver2:
    image: apache/rocketmq:4.9.4
    hostname: rocketmq-nameserver2
    ports:
      - "9877:9876"
    environment:
      JAVA_OPT: "-Duser.home=/opt -Xms512m -Xmx512m"
    command: sh mqnamesrv
    volumes:
      - rocketmq_nameserver2_logs:/opt/logs
    networks:
      - shiku-cluster

  rocketmq-broker:
    image: apache/rocketmq:4.9.4
    hostname: rocketmq-broker
    ports:
      - "10909:10909"
      - "10911:10911"
    environment:
      JAVA_OPT: "-Duser.home=/opt -Xms1g -Xmx1g"
      NAMESRV_ADDR: "rocketmq-nameserver1:9876;rocketmq-nameserver2:9876"
    command: sh mqbroker -n rocketmq-nameserver1:9876;rocketmq-nameserver2:9876 -c /opt/rocketmq-4.9.4/conf/broker.conf
    volumes:
      - rocketmq_broker_logs:/opt/logs
      - rocketmq_broker_store:/opt/store
    depends_on:
      - rocketmq-nameserver1
      - rocketmq-nameserver2
    networks:
      - shiku-cluster

  # ========== Nginx负载均衡器 ==========
  nginx:
    image: nginx:alpine
    hostname: nginx
    ports:
      - "80:80"
      - "5666:5666"
      - "5280:5280"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - shiku-im-1
      - shiku-im-2
      - shiku-im-3
    networks:
      - shiku-cluster

  # ========== Shiku IM服务器集群 ==========
  shiku-im-1:
    build: .
    hostname: shiku-im-1
    ports:
      - "5666:5666"
      - "5280:5280"
    environment:
      - NODE_ID=1
      - CLUSTER_ENABLED=true
    volumes:
      - ./src/main/resources/imserver.properties:/app/config/imserver.properties
      - shiku_logs_1:/app/logs
    depends_on:
      - zk1
      - zk2
      - zk3
      - mongodb1
      - mongodb2
      - mongodb3
      - redis-cluster-1
      - redis-cluster-2
      - redis-cluster-3
      - rocketmq-nameserver1
      - rocketmq-nameserver2
    networks:
      - shiku-cluster

  shiku-im-2:
    build: .
    hostname: shiku-im-2
    ports:
      - "5667:5666"
      - "5281:5280"
    environment:
      - NODE_ID=2
      - CLUSTER_ENABLED=true
    volumes:
      - ./src/main/resources/imserver.properties:/app/config/imserver.properties
      - shiku_logs_2:/app/logs
    depends_on:
      - zk1
      - zk2
      - zk3
      - mongodb1
      - mongodb2
      - mongodb3
      - redis-cluster-1
      - redis-cluster-2
      - redis-cluster-3
      - rocketmq-nameserver1
      - rocketmq-nameserver2
    networks:
      - shiku-cluster

  shiku-im-3:
    build: .
    hostname: shiku-im-3
    ports:
      - "5668:5666"
      - "5282:5280"
    environment:
      - NODE_ID=3
      - CLUSTER_ENABLED=true
    volumes:
      - ./src/main/resources/imserver.properties:/app/config/imserver.properties
      - shiku_logs_3:/app/logs
    depends_on:
      - zk1
      - zk2
      - zk3
      - mongodb1
      - mongodb2
      - mongodb3
      - redis-cluster-1
      - redis-cluster-2
      - redis-cluster-3
      - rocketmq-nameserver1
      - rocketmq-nameserver2
    networks:
      - shiku-cluster

volumes:
  # Zookeeper数据卷
  zk1_data:
  zk1_logs:
  zk2_data:
  zk2_logs:
  zk3_data:
  zk3_logs:
  
  # MongoDB数据卷
  mongodb1_data:
  mongodb2_data:
  mongodb3_data:
  
  # Redis数据卷
  redis1_data:
  redis2_data:
  redis3_data:
  
  # RocketMQ数据卷
  rocketmq_nameserver1_logs:
  rocketmq_nameserver2_logs:
  rocketmq_broker_logs:
  rocketmq_broker_store:
  
  # Shiku IM日志卷
  shiku_logs_1:
  shiku_logs_2:
  shiku_logs_3:

networks:
  shiku-cluster:
    driver: bridge
