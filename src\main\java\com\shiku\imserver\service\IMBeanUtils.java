package com.shiku.imserver.service;

import com.mongodb.MongoClient;
import com.shiku.imserver.ImServerGroupContext;
import com.shiku.imserver.common.ImConfig;
import com.shiku.imserver.message.processor.MessageProcess;
import com.shiku.imserver.repository.GroupRepository;
import com.shiku.imserver.repository.MessageRepository;
import com.shiku.imserver.repository.ServiceRepository;


public class IMBeanUtils {
    private static IMBeanManager beanManager;

    public static ImConfig getImconfig() {
        return beanManager.getImConfig();
    }

    public static void initialize(ImConfig imConfig, ImServerGroupContext groupContext) {
        beanManager = new IMBeanManager();
        beanManager.setImConfig(imConfig);
        beanManager.setGroupContext(groupContext);
        beanManager.initialize();
    }


    public static ImServerGroupContext getServerGroupContext() {
        return beanManager.getGroupContext();
    }

    public static MessageProcess getMessageProcess() {
        return beanManager.getMessageProcess();
    }

    public static IMBeanManager getBeanManager() {
        return beanManager;
    }

    public static MessageRepository getMessageRepository() {
        return beanManager.getMessageRepository();
    }

    public static GroupRepository getGroupRepository() {
        return beanManager.getGroupRepository();
    }

    public static RedisService getRedisService() {
        return beanManager.getRedisService();
    }


    public static RocketmqService getRocketmqService() {
        return beanManager.getRocketmqService();
    }


    public static ReceiptLogicService getReceiptLogicService() {
        return beanManager.getReceiptLogicService();
    }


    public static ServiceRepository getServiceRepository() {
        return beanManager.getServiceRepository();
    }


    public static MongoClient getMongoClient() {
        return beanManager.getMongoClient();
    }
}
