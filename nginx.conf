events {
    worker_connections 1024;
}

http {
    # WebSocket负载均衡
    upstream shiku_websocket {
        # 使用IP哈希确保同一用户连接到同一服务器
        ip_hash;
        server shiku-im-1:5280;
        server shiku-im-2:5280;
        server shiku-im-3:5280;
    }

    # HTTP API负载均衡
    upstream shiku_http {
        server shiku-im-1:8080;
        server shiku-im-2:8080;
        server shiku-im-3:8080;
    }

    # WebSocket代理配置
    server {
        listen 5280;
        server_name _;

        location / {
            proxy_pass http://shiku_websocket;
            proxy_http_version 1.1;
            proxy_set_header Upgrade $http_upgrade;
            proxy_set_header Connection "upgrade";
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # WebSocket超时设置
            proxy_read_timeout 86400;
            proxy_send_timeout 86400;
            proxy_connect_timeout 60;
        }
    }

    # HTTP API代理配置
    server {
        listen 80;
        server_name _;

        location / {
            proxy_pass http://shiku_http;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }
}

# TCP负载均衡（需要nginx stream模块）
stream {
    upstream shiku_tcp {
        # 使用一致性哈希确保同一用户连接到同一服务器
        hash $remote_addr consistent;
        server shiku-im-1:5666;
        server shiku-im-2:5666;
        server shiku-im-3:5666;
    }

    server {
        listen 5666;
        proxy_pass shiku_tcp;
        proxy_timeout 1s;
        proxy_responses 1;
        proxy_connect_timeout 1s;
    }
}
