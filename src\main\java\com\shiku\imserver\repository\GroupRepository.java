
package com.shiku.imserver.repository;


import com.mongodb.client.MongoCursor;
import com.mongodb.client.MongoDatabase;
import com.shiku.imserver.common.IService;
import com.shiku.imserver.common.ImConfig;
import com.shiku.imserver.service.IMBeanUtils;

import java.util.ArrayList;
import java.util.List;

import org.bson.Document;
import org.bson.conversions.Bson;


public class GroupRepository
        extends BaseRepository
        implements IService {
    private static final String ROOMJID_LIST = "shiku_roomJids_userId";
    private MongoDatabase userJidListDB;


    @Override
    public boolean initialize() {

        ImConfig.MongoConfig mongoConfig = IMBeanUtils.getImconfig().getMongoConfig();

        try {

            this.userJidListDB = IMBeanUtils.getBeanManager().getMongoClient().getDatabase("shiku_roomJids_userId");

        } catch (Exception ex) {

            ex.printStackTrace();

            return false;

        }


        return true;

    }


    public List<String> queryUserRoomsJidList(int userId) {

        List<String> jids = new ArrayList<>();

        MongoCursor<String> iterator = getCollection(this.userJidListDB, userId).distinct("jid", (Bson) new Document("userId", Integer.valueOf(userId)), String.class).iterator();


        try {

            while (iterator.hasNext()) {

                String jid = (String) iterator.next();

                jids.add(jid);

            }

        } finally {

            if (null != iterator) {
                iterator.close();
            }

        }

        return jids;

    }

}


