
package com.shiku.imserver.common.constant;


public class KConstants {
    public static boolean isDebug = true;
    public static boolean messageDebug = false;
    public static final String PAGE_INDEX = "0";
    public static final String PAGE_SIZE = "15";
    public static final int MIN_USERID = 100000;


    public static interface SystemNo {
        public static final int System = 10000;
        public static final int NewKFriend = 10001;
        public static final int Circle = 10002;
        public static final int AddressBook = 10003;
        public static final int Notice = 10006;

    }


    public static interface PUSHSERVER {
        public static final String APNS = "apns";
        public static final String APNS_VOIP = "apns_voip";
        public static final String BAIDU = "baidu";
        public static final String XIAOMI = "xiaomi";
        public static final String HUAWEI = "huawei";
        public static final String JPUSH = "Jpush";
        public static final String FCM = "fcm";

    }


    public static interface DeviceKey {
        public static final String Android = "android";
        public static final String IOS = "ios";
        public static final String WEB = "web";
        public static final String PC = "pc";
        public static final String MAC = "mac";

    }


    public static interface Expire {
        public static final int DAY1 = 86400;
        public static final int DAY7 = 604800;
        public static final int HOUR12 = 43200;
        public static final int HOUR = 3600;

    }


    public static interface DB_REMAINDER {
        public static final int ADDRESSBOOK = 10000;

    }

}


