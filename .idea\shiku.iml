<?xml version="1.0" encoding="UTF-8"?>
<module org.jetbrains.idea.maven.project.MavenProjectsManager.isMavenModule="true" type="JAVA_MODULE" version="4">
  <component name="FacetManager">
    <facet type="Spring" name="Spring">
      <configuration />
    </facet>
    <facet type="JRebel" name="JRebel">
      <configuration>
        <option name="ideModuleStorage">
          <map>
            <entry key="com.zeroturnaround.jrebel.FormatVersion" value="7.0.0" />
            <entry key="com.zeroturnaround.jrebel.remoting.DeleteUnindexedFiles" value="false" />
            <entry key="com.zeroturnaround.jrebel.remoting.ModuleRemoteServerSelection" value="off" />
            <entry key="com.zeroturnaround.jrebel.remoting.ModuleSpecificSingleRemoteServer" />
            <entry key="jrebelEnabled" value="true" />
            <entry key="lastExternalPluginCheckTime" value="1600337020534" />
          </map>
        </option>
      </configuration>
    </facet>
  </component>
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_1_8">
    <output url="file://$MODULE_DIR$/target/classes" />
    <output-test url="file://$MODULE_DIR$/target/test-classes" />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/src/main/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/resources" type="java-resource" />
      <excludeFolder url="file://$MODULE_DIR$/target" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter:2.0.3.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot:2.0.3.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-context:5.0.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-aop:5.0.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-beans:5.0.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-expression:5.0.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-autoconfigure:2.0.3.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-logging:2.0.3.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: ch.qos.logback:logback-classic:1.2.3" level="project" />
    <orderEntry type="library" name="Maven: ch.qos.logback:logback-core:1.2.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-to-slf4j:2.10.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-api:2.10.0" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:jul-to-slf4j:1.7.25" level="project" />
    <orderEntry type="library" name="Maven: javax.annotation:javax.annotation-api:1.3.2" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-core:5.0.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-jcl:5.0.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.yaml:snakeyaml:1.19" level="project" />
    <orderEntry type="library" name="Maven: org.t-io:tio-http-common:3.2.8.v20190326-RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.t-io:tio-websocket-server:3.2.8.v20190326-RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.t-io:tio-websocket-common:3.2.8.v20190326-RELEASE" level="project" />
    <orderEntry type="library" name="Maven: commons-codec:commons-codec:1.11" level="project" />
    <orderEntry type="library" name="Maven: org.t-io:tio-http-server:3.2.8.v20190326-RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.esotericsoftware:reflectasm:1.11.9" level="project" />
    <orderEntry type="library" name="Maven: org.t-io:tio-utils:3.2.8.v20190326-RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:slf4j-api:1.7.25" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba:fastjson:1.2.56" level="project" />
    <orderEntry type="library" name="Maven: com.github.ben-manes.caffeine:caffeine:2.6.2" level="project" />
    <orderEntry type="library" name="Maven: org.t-io:tio-core:3.2.8.v20190326-RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.mongodb:mongo-java-driver:3.5.0" level="project" />
    <orderEntry type="library" name="Maven: org.redisson:redisson:3.10.0" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-common:4.1.25.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-codec:4.1.25.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-buffer:4.1.25.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-transport:4.1.25.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-resolver:4.1.25.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-resolver-dns:4.1.25.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-codec-dns:4.1.25.Final" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-handler:4.1.25.Final" level="project" />
    <orderEntry type="library" name="Maven: javax.cache:cache-api:1.1.0" level="project" />
    <orderEntry type="library" name="Maven: io.projectreactor:reactor-core:3.1.8.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.reactivestreams:reactive-streams:1.0.2" level="project" />
    <orderEntry type="library" name="Maven: io.reactivex.rxjava2:rxjava:2.1.14" level="project" />
    <orderEntry type="library" name="Maven: de.ruedigermoeller:fst:2.56" level="project" />
    <orderEntry type="library" name="Maven: org.javassist:javassist:3.21.0-GA" level="project" />
    <orderEntry type="library" name="Maven: org.objenesis:objenesis:2.5.1" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.dataformat:jackson-dataformat-yaml:2.9.6" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-core:2.9.6" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-databind:2.9.6" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-annotations:2.9.0" level="project" />
    <orderEntry type="library" name="Maven: net.bytebuddy:byte-buddy:1.7.11" level="project" />
    <orderEntry type="library" name="Maven: org.jodd:jodd-bean:3.7.1" level="project" />
    <orderEntry type="library" name="Maven: org.jodd:jodd-core:3.7.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.rocketmq:rocketmq-client:4.3.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.rocketmq:rocketmq-common:4.3.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.rocketmq:rocketmq-remoting:4.3.2" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-all:4.1.25.Final" level="project" />
    <orderEntry type="library" name="Maven: org.apache.rocketmq:rocketmq-logging:4.3.2" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-tcnative-boringssl-static:1.1.33.Fork26" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-lang3:3.7" level="project" />
    <orderEntry type="library" name="Maven: com.google.protobuf:protobuf-java:3.6.1" level="project" />
    <orderEntry type="library" name="Maven: com.google.protobuf:protobuf-java-util:3.6.1" level="project" />
    <orderEntry type="library" name="Maven: com.google.code.gson:gson:2.8.5" level="project" />
    <orderEntry type="library" name="Maven: com.google.guava:guava:19.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework.boot:spring-boot-starter-test:2.0.3.RELEASE" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework.boot:spring-boot-test:2.0.3.RELEASE" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework.boot:spring-boot-test-autoconfigure:2.0.3.RELEASE" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: com.jayway.jsonpath:json-path:2.4.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: net.minidev:json-smart:2.3" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: net.minidev:accessors-smart:1.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.ow2.asm:asm:5.0.4" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: junit:junit:4.12" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.assertj:assertj-core:3.9.1" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.mockito:mockito-core:2.15.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: net.bytebuddy:byte-buddy-agent:1.7.11" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.hamcrest:hamcrest-core:1.3" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.hamcrest:hamcrest-library:1.3" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.skyscreamer:jsonassert:1.5.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: com.vaadin.external.google:android-json:0.0.20131108.vaadin1" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework:spring-test:5.0.7.RELEASE" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.xmlunit:xmlunit-core:2.5.1" level="project" />
  </component>
</module>