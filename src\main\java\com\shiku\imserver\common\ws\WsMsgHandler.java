
package com.shiku.imserver.common.ws;


import com.shiku.imserver.common.http.HttpRequest;
import com.shiku.imserver.common.http.HttpResponse;
import org.tio.core.ChannelContext;


public class WsMsgHandler
        implements IWsMsgHandler {

    @Override
    public HttpResponse handshake(HttpRequest httpRequest, HttpResponse httpResponse, ChannelContext channelContext) throws Exception {

        return httpResponse;

    }


    @Override
    public void onAfterHandshaked(HttpRequest httpRequest, HttpResponse httpResponse, ChannelContext channelContext) throws Exception {
    }


    @Override
    public Object onBytes(WsRequest wsRequest, byte[] bytes, ChannelContext channelContext) throws Exception {

        return null;

    }


    @Override
    public Object onClose(WsRequest wsRequest, byte[] bytes, ChannelContext channelContext) throws Exception {

        return null;

    }


    @Override
    public Object onText(WsRequest wsRequest, String text, ChannelContext channelContext) throws Exception {

        return null;

    }

}


