
package com.shiku.imserver.repository.runnable;


import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.shiku.commons.thread.pool.AbstractMapRunnable;
import org.bson.Document;


public abstract class BaseRepositoryMapRunnable<T>
        extends AbstractMapRunnable<T> {
    protected static final int MIN_USERID = 100000;
    protected static final int DB_REMAINDER = 10000;


    protected MongoCollection<Document> getCollection(MongoDatabase database, String userId) throws Exception {

        String collectionName = getCollectionName(Long.valueOf(userId).longValue());

        return database.getCollection(collectionName);

    }


    protected String getCollectionName(long userId) throws Exception {

        long remainder = 0L;

        if (userId > 100000L) {

            remainder = userId / 10000L;

        }

        return String.valueOf(remainder);

    }


    protected String getCollectionName(String userIdStr) throws Exception {

        long remainder = 0L;

        long userId = Long.valueOf(userIdStr).longValue();

        if (userId > 100000L) {

            remainder = userId / 10000L;

        }

        return String.valueOf(remainder);

    }

}


