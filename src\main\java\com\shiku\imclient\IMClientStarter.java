package com.shiku.imclient;

import com.shiku.imserver.common.message.AuthMessage;
import com.shiku.imserver.common.message.ChatMessage;
import com.shiku.imserver.common.message.MessageHead;
import com.shiku.imserver.common.packets.ImPacket;
import com.shiku.imserver.common.utils.Callback;
import com.shiku.imserver.common.utils.ProBufUtils;
import com.shiku.imserver.common.utils.StringUtils;
import com.shiku.imserver.common.utils.ThreadUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.core.ChannelContext;
import org.tio.core.intf.Packet;

public class IMClientStarter {
    static String serverIp = "***************";
    static int serverPort = 5666;
    static final Logger log = LoggerFactory.getLogger(BaseIMClient.class);
    static boolean sendMsg = true;
    static long seelp = 10000L;
    static BaseClientHandler clientHandler = new BaseClientHandler() {
        @Override
        public void handlerReceipt(String messageId) {
        }

        @Override
        public Packet heartbeatPacket(ChannelContext channelContext) {
            MessageHead messageHead = new MessageHead();
            ChatMessage message = new ChatMessage();
            messageHead.setChatType((byte) 1);
            messageHead.setFrom(channelContext.userid + "/Server");
            messageHead.setTo("200000");
            message.setFromUserId(channelContext.userid);
            message.setFromUserName(channelContext.userid);
            message.setToUserId("200000");
            message.setToUserName("200000");
            message.setContent("1111111");
            message.setType((short) 1);
            message.setMessageHead(messageHead);
            ImPacket packet = ProBufUtils.encodeTcpPacket(message, com.shiku.imserver.common.proto.MessageProBuf.ChatMessage.getDescriptor());
            packet.setCommand((short) 10);
            return packet;
        }
    };
    static BaseClientListener clientListener = new BaseClientListener() {
        @Override
        public AuthMessage authUserMessage(ChannelContext channelContext, BaseIMClient imClient) {
            return null;
        }
    };

    public IMClientStarter() {
    }

    public static void main(String[] args) throws Exception {
        int connCount = 100;
        if (1 < args.length) {
            try {
                String count = args[1];
                connCount = Integer.valueOf(count);
            } catch (Exception var6) {
                log.error(var6.getMessage());
            }
        }

        if (2 < args.length) {
            try {
                serverIp = args[2];
            } catch (Exception var5) {
                log.error(var5.getMessage());
            }
        }

        if (3 < args.length) {
            try {
                sendMsg = "1".equals(args[3]);
            } catch (Exception var4) {
                log.error(var4.getMessage());
            }
        }

        if (4 < args.length) {
            try {
                seelp = Long.valueOf(args[4]);
            } catch (Exception var3) {
                log.error(var3.getMessage());
            }
        }

        System.out.println("connCount  =====> " + connCount);
        System.out.println("serverIp  =====> " + serverIp);
        System.out.println("sendMsg  =====> " + sendMsg);
        System.out.println("seelp  =====> " + seelp);

        for (int i = 0; i < connCount; ++i) {
            ThreadUtil.executeInThread(new Callback() {
                @Override
                public void execute(Object obj) {
                    IMClient client = new IMClient();
                    String userId = System.currentTimeMillis() + "";
                    String resource = StringUtils.randomCode();
                    client.setUserId(userId);
                    client.setResource(resource);

                    try {
                        client.initIMClient(IMClientStarter.serverIp, IMClientStarter.serverPort, IMClientStarter.clientHandler, IMClientStarter.clientListener);
                    } catch (Exception var6) {
                        IMClientStarter.log.error(var6.toString(), var6);
                    }

                }
            });
        }

    }

    public static void whileSendMsg(IMClient imClient) {
        try {
            Thread.sleep(seelp);
        } catch (InterruptedException var5) {
            var5.printStackTrace();
        }

        while (true) {
            MessageHead messageHead = new MessageHead();
            ChatMessage message = new ChatMessage();
            messageHead.setChatType((byte) 1);
            messageHead.setFrom(imClient.getConnStr());
            messageHead.setTo("200000");
            message.setFromUserId(imClient.getUserId());
            message.setFromUserName(imClient.getUserId());
            message.setToUserId("200000");
            message.setToUserName("200000");
            message.setContent("1111111");
            message.setType((short) 1);
            message.setMessageHead(messageHead);
            imClient.sendMessage(message);

            try {
                Thread.sleep(seelp);
            } catch (InterruptedException var4) {
                var4.printStackTrace();
            }
        }
    }
}
