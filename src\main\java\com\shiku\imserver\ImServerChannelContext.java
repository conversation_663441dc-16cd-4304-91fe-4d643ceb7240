
package com.shiku.imserver;


import java.nio.channels.AsynchronousSocketChannel;

import org.tio.core.GroupContext;
import org.tio.server.ServerChannelContext;


public class ImServerChannelContext
        extends ServerChannelContext {

    public ImServerChannelContext(GroupContext groupContext) {

        super(groupContext);

    }


    public ImServerChannelContext(GroupContext groupContext, AsynchronousSocketChannel asynchronousSocketChannel) {

        super(groupContext, asynchronousSocketChannel);

    }

}


