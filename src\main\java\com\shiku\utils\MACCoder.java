
package com.shiku.utils;


import java.security.NoSuchAlgorithmException;
import javax.crypto.KeyGenerator;
import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import javax.xml.bind.annotation.adapters.HexBinaryAdapter;


public class MACCoder {

    public static byte[] initHmacMD5Key() throws NoSuchAlgorithmException {

        KeyGenerator generator = KeyGenerator.getInstance("HmacMD5");


        SecretKey secretKey = generator.generateKey();


        byte[] key = secretKey.getEncoded();

        return key;

    }


    public static String encodeHmacMD5(byte[] data, byte[] key) throws Exception {

        SecretKey secretKey = new SecretKeySpec(key, "HmacMD5");


        Mac mac = Mac.getInstance(secretKey.getAlgorithm());


        mac.init(secretKey);


        byte[] digest = mac.doFinal(data);

        return (new HexBinaryAdapter()).marshal(digest);

    }


    public static byte[] initHmacSHAKey() throws NoSuchAlgorithmException {

        KeyGenerator generator = KeyGenerator.getInstance("HmacSHA1");


        SecretKey secretKey = generator.generateKey();


        byte[] key = secretKey.getEncoded();

        return key;

    }


    public static String encodeHmacSHA(byte[] data, byte[] key) throws Exception {

        SecretKey secretKey = new SecretKeySpec(key, "HmacSHA1");


        Mac mac = Mac.getInstance(secretKey.getAlgorithm());


        mac.init(secretKey);


        byte[] digest = mac.doFinal(data);

        return (new HexBinaryAdapter()).marshal(digest);

    }


    public static byte[] initHmacSHA256Key() throws NoSuchAlgorithmException {

        KeyGenerator generator = KeyGenerator.getInstance("HmacSHA256");


        SecretKey secretKey = generator.generateKey();


        byte[] key = secretKey.getEncoded();

        return key;

    }


    public static String encodeHmacSHA256(byte[] data, byte[] key) throws Exception {

        SecretKey secretKey = new SecretKeySpec(key, "HmacSHA256");


        Mac mac = Mac.getInstance(secretKey.getAlgorithm());


        mac.init(secretKey);


        byte[] digest = mac.doFinal(data);

        return (new HexBinaryAdapter()).marshal(digest);

    }


    public static byte[] initHmacSHA384Key() throws NoSuchAlgorithmException {

        KeyGenerator generator = KeyGenerator.getInstance("HmacSHA384");


        SecretKey secretKey = generator.generateKey();


        byte[] key = secretKey.getEncoded();

        return key;

    }


    public static String encodeHmacSHA384(byte[] data, byte[] key) throws Exception {

        SecretKey secretKey = new SecretKeySpec(key, "HmacSHA384");


        Mac mac = Mac.getInstance(secretKey.getAlgorithm());


        mac.init(secretKey);


        byte[] digest = mac.doFinal(data);

        return (new HexBinaryAdapter()).marshal(digest);

    }


    public static byte[] initHmacSHA512Key() throws NoSuchAlgorithmException {

        KeyGenerator generator = KeyGenerator.getInstance("HmacSHA512");


        SecretKey secretKey = generator.generateKey();


        byte[] key = secretKey.getEncoded();

        return key;

    }


    public static String encodeHmacSHA512(byte[] data, byte[] key) throws Exception {

        SecretKey secretKey = new SecretKeySpec(key, "HmacSHA512");


        Mac mac = Mac.getInstance(secretKey.getAlgorithm());


        mac.init(secretKey);


        byte[] digest = mac.doFinal(data);

        return (new HexBinaryAdapter()).marshal(digest);

    }

}


