package com.shiku.imserver.common.db;

import com.mongodb.MongoClient;
import com.mongodb.MongoClientOptions;
import com.mongodb.MongoClientURI;
import com.mongodb.ReadPreference;
import com.mongodb.WriteConcern;
import com.mongodb.event.ConnectionPoolEventListener;
import com.mongodb.event.ConnectionPoolOpenedEvent;
import com.mongodb.event.ConnectionPoolClosedEvent;
import com.mongodb.event.ConnectionCheckedOutEvent;
import com.mongodb.event.ConnectionCheckedInEvent;
import com.shiku.imserver.common.ImConfig;
import com.shiku.imserver.service.IMBeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 增强的MongoDB连接管理器
 * 提供连接池监控、负载均衡、故障恢复等功能
 */
public class EnhancedMongoClientManager {
    
    private static final Logger logger = LoggerFactory.getLogger(EnhancedMongoClientManager.class);
    
    private MongoClient primaryClient;
    private MongoClient secondaryClient;
    private final AtomicInteger activeConnections = new AtomicInteger(0);
    private final AtomicLong totalConnectionsCreated = new AtomicLong(0);
    private final AtomicLong totalConnectionsClosed = new AtomicLong(0);
    private volatile boolean isHealthy = true;
    
    /**
     * 创建增强的MongoDB客户端
     */
    public MongoClient createEnhancedMongoClient(ImConfig.MongoConfig mongoConfig) {
        try {
            MongoClientOptions.Builder optionsBuilder = MongoClientOptions.builder();
            
            // 基础连接配置
            optionsBuilder.connectTimeout(mongoConfig.getConnectTimeout())
                          .socketTimeout(mongoConfig.getSocketTimeout())
                          .maxWaitTime(mongoConfig.getMaxWaitTime());
            
            // 连接池配置
            optionsBuilder.minConnectionsPerHost(mongoConfig.getMinConnectionsPerHost())
                          .connectionsPerHost(mongoConfig.getMaxConnectionsPerHost())
                          .threadsAllowedToBlockForConnectionMultiplier(mongoConfig.getThreadsAllowedToBlockForConnectionMultiplier())
                          .maxConnectionIdleTime(mongoConfig.getMaxConnectionIdleTime())
                          .maxConnectionLifeTime(mongoConfig.getMaxConnectionLifeTime());
            
            // 心跳配置
            optionsBuilder.heartbeatFrequency(mongoConfig.getHeartbeatFrequency())
                          .minHeartbeatFrequency(mongoConfig.getMinHeartbeatFrequency())
                          .heartbeatConnectTimeout(mongoConfig.getHeartbeatConnectTimeout())
                          .heartbeatSocketTimeout(mongoConfig.getHeartbeatSocketTimeout());
            
            // Socket配置
            optionsBuilder.socketKeepAlive(mongoConfig.isSocketKeepAlive());
            
            // 读写配置
            optionsBuilder.readPreference(ReadPreference.secondaryPreferred())
                          .writeConcern(WriteConcern.MAJORITY);
            
            // 添加连接池监听器
            optionsBuilder.addConnectionPoolListener(new EnhancedConnectionPoolListener());
            
            MongoClientURI uri = new MongoClientURI(mongoConfig.getUri(), optionsBuilder);
            MongoClient client = new MongoClient(uri);
            
            logger.info("Enhanced MongoDB client created successfully with connection pool monitoring");
            return client;
            
        } catch (Exception e) {
            logger.error("Failed to create enhanced MongoDB client", e);
            throw new RuntimeException("MongoDB client creation failed", e);
        }
    }
    
    /**
     * 获取主数据库客户端（写操作）
     */
    public MongoClient getPrimaryClient() {
        if (primaryClient == null) {
            synchronized (this) {
                if (primaryClient == null) {
                    primaryClient = createEnhancedMongoClient(IMBeanUtils.getImconfig().getMongoConfig());
                }
            }
        }
        return primaryClient;
    }
    
    /**
     * 获取从数据库客户端（读操作）
     */
    public MongoClient getSecondaryClient() {
        if (secondaryClient == null) {
            synchronized (this) {
                if (secondaryClient == null) {
                    // 为读操作创建单独的客户端配置
                    ImConfig.MongoConfig readConfig = IMBeanUtils.getImconfig().getMongoConfig();
                    secondaryClient = createEnhancedMongoClient(readConfig);
                }
            }
        }
        return secondaryClient;
    }
    
    /**
     * 获取适合的客户端（读写分离）
     */
    public MongoClient getClientForOperation(boolean isWriteOperation) {
        if (isWriteOperation) {
            return getPrimaryClient();
        } else {
            // 读操作优先使用从库
            return getSecondaryClient();
        }
    }
    
    /**
     * 获取连接池状态
     */
    public ConnectionPoolStats getConnectionPoolStats() {
        return new ConnectionPoolStats(
            activeConnections.get(),
            totalConnectionsCreated.get(),
            totalConnectionsClosed.get(),
            isHealthy
        );
    }
    
    /**
     * 健康检查
     */
    public boolean isHealthy() {
        try {
            if (primaryClient != null) {
                primaryClient.getDatabase("admin").runCommand(new org.bson.Document("ping", 1));
            }
            if (secondaryClient != null) {
                secondaryClient.getDatabase("admin").runCommand(new org.bson.Document("ping", 1));
            }
            isHealthy = true;
            return true;
        } catch (Exception e) {
            logger.warn("MongoDB health check failed", e);
            isHealthy = false;
            return false;
        }
    }
    
    /**
     * 关闭连接
     */
    public void close() {
        try {
            if (primaryClient != null) {
                primaryClient.close();
                primaryClient = null;
            }
            if (secondaryClient != null) {
                secondaryClient.close();
                secondaryClient = null;
            }
            logger.info("MongoDB clients closed successfully");
        } catch (Exception e) {
            logger.error("Error closing MongoDB clients", e);
        }
    }
    
    /**
     * 连接池事件监听器
     */
    private class EnhancedConnectionPoolListener implements ConnectionPoolEventListener {
        
        @Override
        public void connectionPoolOpened(ConnectionPoolOpenedEvent event) {
            logger.info("MongoDB connection pool opened: {}", event.getServerId());
        }
        
        @Override
        public void connectionPoolClosed(ConnectionPoolClosedEvent event) {
            logger.info("MongoDB connection pool closed: {}", event.getServerId());
        }
        
        @Override
        public void connectionCheckedOut(ConnectionCheckedOutEvent event) {
            activeConnections.incrementAndGet();
            totalConnectionsCreated.incrementAndGet();
            logger.debug("Connection checked out: {}, Active: {}", 
                event.getConnectionId(), activeConnections.get());
        }
        
        @Override
        public void connectionCheckedIn(ConnectionCheckedInEvent event) {
            activeConnections.decrementAndGet();
            totalConnectionsClosed.incrementAndGet();
            logger.debug("Connection checked in: {}, Active: {}", 
                event.getConnectionId(), activeConnections.get());
        }
    }
    
    /**
     * 连接池统计信息
     */
    public static class ConnectionPoolStats {
        private final int activeConnections;
        private final long totalConnectionsCreated;
        private final long totalConnectionsClosed;
        private final boolean isHealthy;
        
        public ConnectionPoolStats(int activeConnections, long totalConnectionsCreated, 
                                 long totalConnectionsClosed, boolean isHealthy) {
            this.activeConnections = activeConnections;
            this.totalConnectionsCreated = totalConnectionsCreated;
            this.totalConnectionsClosed = totalConnectionsClosed;
            this.isHealthy = isHealthy;
        }
        
        public int getActiveConnections() { return activeConnections; }
        public long getTotalConnectionsCreated() { return totalConnectionsCreated; }
        public long getTotalConnectionsClosed() { return totalConnectionsClosed; }
        public boolean isHealthy() { return isHealthy; }
        
        @Override
        public String toString() {
            return String.format("ConnectionPoolStats{active=%d, created=%d, closed=%d, healthy=%s}",
                activeConnections, totalConnectionsCreated, totalConnectionsClosed, isHealthy);
        }
    }
}
