
package com.shiku.utils;


public class PayConstants {
    public static final String configUrl = "config";
    public static final String SUCCESS_CODE = "10000";
    public static final String FAIL_CODE = "40004";
    public static final String SIGN_CHARSET = "UTF-8";
    public static final String CHARSET = "UTF-8";
    public static final String SIGN_TYPE = "RSA";
    public static final String LOG_PATH = "D:\\";
    public static final String INPUT_CHARSET = "UTF-8";
    public static final String MD5_SIGNTYPE = "MD5";
    public static final String RSA_SIGNTYPE = "RSA";
    public static final String ALIPAY_GATEWAY = "https://openapi.alipay.com/gateway.do";
    public static final String GRANT_TYPE = "authorization_code";
    public static String ALI_PUBLIC_KEY = "";
    public static final String PREPAY_ID_URL = "https://api.mch.weixin.qq.com/pay/unifiedorder";
    public static final String QUERY_URL = "https://api.mch.weixin.qq.com/pay/orderquery";
    public static final String WX_APPID = "";
    public static final String WXAPPSECRET = "";
    public static final String WX_PARTNERKEY = "";
    public static final String WXMCH_ID = "";
    public static final String WXSPBILL_CREATE_IP = "";
    public static final String TRADE_TYPE_JS = "NATIVE";
    public static final String WX_JSAPI = "APP";
    public static final String WXPAY_NOTIFY_URL = "http://www.youjob.co:8092/user/recharge/wxPayCallBack";
    public static final String WXJSPAY_NOTIFY_URL = "http://www.youjob.co:8092/user/recharge/wxPayCallBack";
    public static final String ALIPAY_NOTIFY_URL = "http://www.youjob.co:8092/user/recharge/aliPayCallBack";
    public static final String ALIPAY_WAP_NOTIFY_URL = "";

}


