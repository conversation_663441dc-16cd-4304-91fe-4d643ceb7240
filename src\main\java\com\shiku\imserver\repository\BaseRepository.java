
package com.shiku.imserver.repository;


import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import com.shiku.imserver.common.AbstractService;
import org.bson.Document;


public abstract class BaseRepository
        extends AbstractService {
    protected static final int MIN_USERID = 100000;
    protected static final int DB_REMAINDER = 10000;


    protected MongoCollection<Document> getCollection(MongoDatabase database, long userId) {

        String collectionName = getCollectionName(database.getName(), userId);

        return database.getCollection(collectionName);

    }


    protected String getCollectionName(String dbName, long userId) {

        long remainder = 0L;

        String collectionName = null;


        if (userId > 100000L) {

            remainder = userId / 10000L;

        }

        collectionName = remainder + "";

        return collectionName;

    }

}


