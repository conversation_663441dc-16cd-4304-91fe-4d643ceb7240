
package com.shiku.imserver;


import com.shiku.imserver.common.ws.IWsMsgHandler;
import com.shiku.imserver.common.ws.WsMsgHandler;
import com.shiku.imserver.common.ws.WsServerConfig;
import com.shiku.imserver.hander.ImServerHandler;
import com.shiku.imserver.hander.TcpServerHandler;
import com.shiku.imserver.hander.WsServerAioHandler;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadFactory;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.server.ServerGroupContext;
import org.tio.server.intf.ServerAioHandler;
import org.tio.server.intf.ServerAioListener;
import org.tio.utils.thread.pool.DefaultThreadFactory;
import org.tio.utils.thread.pool.SynThreadPoolExecutor;


public class ImServerGroupContext
        extends ServerGroupContext {
    private Logger log = LoggerFactory.getLogger(ImServerGroupContext.class);

    private static int CORE_POOL_SIZE = Runtime.getRuntime().availableProcessors() * 2;

    protected SynThreadPoolExecutor timExecutor = null;

    private ServerAioHandler wsServerAioHandler = null;

    private ServerAioListener wsServerAioListener = null;

    private ServerAioHandler tcpServerAioHandler = null;

    private ServerAioListener tcpServerAioListener = null;


    public ImServerGroupContext(ImServerHandler imServerAioHandler, ServerAioListener imServerAioListener) {

        super("Shiku IM Server", (ServerAioHandler) imServerAioHandler, imServerAioListener);

        this.tcpServerAioHandler = (ServerAioHandler) new TcpServerHandler();


        WsServerConfig wsServerConfig = new WsServerConfig(Integer.valueOf(5280), true);

        this.wsServerAioHandler = (ServerAioHandler) new WsServerAioHandler(wsServerConfig, (IWsMsgHandler) new WsMsgHandler());


        if (this.timExecutor == null) {

            LinkedBlockingQueue<Runnable> timQueue = new LinkedBlockingQueue<>();

            String timThreadName = "Shiku IM Server";

            this
                    .timExecutor = new SynThreadPoolExecutor(CORE_POOL_SIZE, CORE_POOL_SIZE, 0L, timQueue, (ThreadFactory) DefaultThreadFactory.getInstance(timThreadName, Integer.valueOf(5)), timThreadName);

            this.timExecutor.prestartAllCoreThreads();

        }

    }


    public SynThreadPoolExecutor getTimExecutor() {

        return this.timExecutor;

    }


    public ServerAioListener getWsServerAioListener() {

        return this.wsServerAioListener;

    }


    public void setWsServerAioListener(ServerAioListener wsServerAioListener) {

        this.wsServerAioListener = wsServerAioListener;

    }


    public ServerAioHandler getWsServerAioHandler() {

        return this.wsServerAioHandler;

    }


    public void setWsServerAioHandler(ServerAioHandler wsServerAioHandler) {

        this.wsServerAioHandler = wsServerAioHandler;

    }


    public ServerAioHandler getTcpServerAioHandler() {

        return this.tcpServerAioHandler;

    }


    public void setTcpServerAioHandler(ServerAioHandler tcpServerAioHandler) {

        this.tcpServerAioHandler = tcpServerAioHandler;

    }


    public ServerAioListener getTcpServerAioListener() {

        return this.tcpServerAioListener;

    }


    public void setTcpServerAioListener(ServerAioListener tcpServerAioListener) {

        this.tcpServerAioListener = tcpServerAioListener;

    }

}


