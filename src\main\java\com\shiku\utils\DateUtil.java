
package com.shiku.utils;


import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.regex.Pattern;


public final class DateUtil {
    public static final SimpleDateFormat FORMAT_YYYY_MM;
    public static final SimpleDateFormat FORMAT_YYYY_MM_DD;
    public static final SimpleDateFormat FORMAT_YMDHMS = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    static {

        FORMAT_YYYY_MM_DD = new SimpleDateFormat("yyyy-MM-dd");

        FORMAT_YYYY_MM = new SimpleDateFormat("yyyy-MM");

        PATTERN_YYYY_MM_DD_HH_MM_SS = Pattern.compile("[0-9]{4}-[0-9]{1,2}-[0-9]{1,2} [0-9]{1,2}:[0-9]{1,2}:[0-9]{1,2}");

        PATTERN_YYYY_MM_DD = Pattern.compile("[0-9]{4}-[0-9]{1,2}-[0-9]{1,2}");

        PATTERN_YYYY_MM = Pattern.compile("[0-9]{4}-[0-9]{1,2}");

    }


    public static final Pattern PATTERN_YYYY_MM;
    public static final Pattern PATTERN_YYYY_MM_DD;
    public static final Pattern PATTERN_YYYY_MM_DD_HH_MM_SS;


    public static synchronized long getSysCurrentTimeMillis_sync() {

        return System.currentTimeMillis();

    }


    public static long currentTimeSeconds() {

        return System.currentTimeMillis() / 1000L;

    }


    public static String getFullString() {

        return (new SimpleDateFormat("yyyy-MM-dd HH:mm:ss")).format(new Date());

    }


    public static String getYMDString(Date date) {

        return (new SimpleDateFormat("yyyyMMdd")).format(date);

    }


    public static String getYMDString() {

        return (new SimpleDateFormat("yyyyMMdd")).format(new Date());

    }


    public static String getYMString() {

        return (new SimpleDateFormat("yyyyMM")).format(new Date());

    }


    public static String getTimeString(long millis) {

        return FORMAT_YMDHMS.format(new Date(millis));

    }


    public static Date toDate(String strDate) {

        strDate = strDate.replaceAll("/", "-");

        try {

            if (PATTERN_YYYY_MM_DD_HH_MM_SS.matcher(strDate).find()) {
                return FORMAT_YMDHMS.parse(strDate);
            }

            if (PATTERN_YYYY_MM_DD.matcher(strDate).find()) {
                return FORMAT_YYYY_MM_DD.parse(strDate);
            }

            if (PATTERN_YYYY_MM.matcher(strDate).find()) {

                return FORMAT_YYYY_MM.parse(strDate);

            }

            throw new RuntimeException("未知的日期格式化字符串");

        } catch (ParseException e) {

            throw new RuntimeException(e);

        }

    }


    public static long toTimestamp(String strDate) {

        return toDate(strDate).getTime();

    }


    public static long toSeconds(String strDate) {

        return toTimestamp(strDate) / 1000L;

    }


    public static long s2s(String s) {

        s = StringUtil.trim(s);

        if ("至今".equals(s)) {

            return 0L;

        }

        return toSeconds(s);

    }


    public static boolean compareDayTime(Calendar d1, Calendar d2) {

        int d1_year = d1.get(1);

        int d1_month = d1.get(2);

        int d1_day = d1.get(5);

        int d2_year = d2.get(1);

        int d2_month = d2.get(2);

        int d2_day = d2.get(5);

        if (d1_year == d2_year && d1_month == d2_month && d1_day == d2_day) {
            return true;
        }

        return false;

    }


    public static boolean compareMonthTime(Calendar d1, Calendar d2) {

        int d1_year = d1.get(1);

        int d1_month = d1.get(2);

        int d2_year = d2.get(1);

        int d2_month = d2.get(2);

        if (d1_year == d2_year && d1_month == d2_month) {
            return true;
        }

        return false;

    }


    public static Date getNextDay(Date currentDay) {

        Calendar calendar = Calendar.getInstance();

        calendar.setTime(currentDay);

        calendar.add(5, 1);

        Date nextDay = calendar.getTime();

        return nextDay;

    }


    public static Date getTodayMorning() {

        Calendar cal = Calendar.getInstance();

        cal.set(11, 0);

        cal.set(13, 0);

        cal.set(12, 0);

        cal.set(14, 0);

        return cal.getTime();

    }


    public static Date getTodayNight() {

        Calendar cal = Calendar.getInstance();

        cal.set(11, 24);

        cal.set(13, 0);

        cal.set(12, 0);

        cal.set(14, 0);

        return cal.getTime();

    }


    public static Date getYesterdayMorning() {

        Calendar cal = Calendar.getInstance();

        cal.add(5, -1);

        cal.set(11, 0);

        cal.set(13, 0);

        cal.set(12, 0);

        cal.set(14, 0);

        return cal.getTime();

    }


    public static Date getYesterdayLastTime() {

        Calendar cal = Calendar.getInstance();

        cal.add(5, -1);

        cal.set(11, 23);

        cal.set(13, 0);

        cal.set(12, 59);

        cal.set(14, 0);

        return cal.getTime();

    }


    public static Date getYesterdayNight() {

        Calendar cal = Calendar.getInstance();

        cal.add(5, -1);

        cal.set(11, 24);

        cal.set(13, 0);

        cal.set(12, 0);

        cal.set(14, 0);

        return cal.getTime();

    }


    public static Date getTomorrowMorning() {

        Calendar cal = Calendar.getInstance();

        cal.add(5, 1);

        cal.set(11, 0);

        cal.set(13, 0);

        cal.set(12, 0);

        cal.set(14, 0);

        return cal.getTime();

    }


    public static Date getTomorrowLastTime() {

        Calendar cal = Calendar.getInstance();

        cal.add(5, 1);

        cal.set(11, 23);

        cal.set(13, 0);

        cal.set(12, 59);

        cal.set(14, 0);

        return cal.getTime();

    }


    public static Date getTomorrowNight() {

        Calendar cal = Calendar.getInstance();

        cal.add(5, 1);

        cal.set(11, 24);

        cal.set(13, 0);

        cal.set(12, 0);

        cal.set(14, 0);

        return cal.getTime();

    }


    public static Date getWeekMorning() {

        Calendar cal = Calendar.getInstance();

        cal.set(cal.get(1), cal.get(2), cal.get(5), 0, 0, 0);

        cal.set(7, 2);

        return cal.getTime();

    }


    public static Date getWeekNight() {

        Calendar cal = Calendar.getInstance();

        cal.setTime(getWeekMorning());

        cal.add(7, 7);

        return cal.getTime();

    }


    public static Date getLastMonth() {

        Calendar cal = Calendar.getInstance();

        cal.add(2, -1);

        return cal.getTime();

    }


    public static Date getMonthMorning() {

        Calendar cal = Calendar.getInstance();

        cal.set(cal.get(1), cal.get(2), cal.get(5), 0, 0, 0);

        cal.set(5, cal.getActualMinimum(5));

        return cal.getTime();

    }


    public static Date getMonthNight() {

        Calendar cal = Calendar.getInstance();

        cal.set(cal.get(1), cal.get(2), cal.get(5), 0, 0, 0);

        cal.set(5, cal.getActualMaximum(5));

        cal.set(11, 24);

        return cal.getTime();

    }


    public static Date getLastMonthMorning() {

        Calendar cal = Calendar.getInstance();

        cal.add(2, -1);

        cal.set(5, 1);

        cal.set(11, 0);

        cal.set(13, 0);

        cal.set(12, 0);

        cal.set(14, 0);

        return cal.getTime();

    }


    public static Date getPreviousWeekday() {

        Calendar cal = Calendar.getInstance();

        cal.setFirstDayOfWeek(2);

        cal.add(5, -7);

        cal.set(7, 2);

        cal.set(11, 0);

        cal.set(13, 0);

        cal.set(12, 0);

        cal.set(14, 0);


        return cal.getTime();

    }


    public static Date getPreviousWeekSunday() {

        Calendar cal = Calendar.getInstance();

        cal.setFirstDayOfWeek(2);

        cal.add(5, -7);

        cal.set(7, 1);

        cal.set(11, 23);

        cal.set(13, 0);

        cal.set(12, 59);

        cal.set(14, 0);

        return cal.getTime();

    }


    public static Date getNextWeekSunday() {

        Calendar cal = Calendar.getInstance();

        cal.setFirstDayOfWeek(2);

        cal.add(5, 7);

        cal.set(7, 1);

        cal.set(11, 23);

        cal.set(13, 0);

        cal.set(12, 59);

        cal.set(14, 0);

        return cal.getTime();

    }


    public static Date getNextDay(int day) {

        Calendar cal = Calendar.getInstance();

        cal.add(5, day);

        cal.set(11, 23);

        cal.set(13, 0);

        cal.set(12, 59);

        cal.set(14, 0);

        return cal.getTime();

    }


    public static Date getNextYear() {

        Calendar cal = Calendar.getInstance();

        cal.setTime(new Date());

        cal.add(1, 1);

        return cal.getTime();

    }


    public static Date getNextYear(int year) {

        Calendar cal = Calendar.getInstance();

        cal.setTime(new Date());

        cal.add(1, year);

        return cal.getTime();

    }


    public static Date strToDateTime(String strDateTime) {

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");

        try {

            return format.parse(strDateTime);

        } catch (ParseException e) {

            e.printStackTrace();


            return null;

        }

    }

    public static String TimeToStr(Date date) {

        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");

        return format.format(date);

    }


    public static Date strYYMMDDToDate(String strYYMMDD) {

        SimpleDateFormat format = new SimpleDateFormat("yy-MM-dd");

        try {

            return format.parse(strYYMMDD);

        } catch (ParseException e) {

            e.printStackTrace();


            return null;

        }

    }


    public static Date getDate(String time, String pattern) throws ParseException {

        SimpleDateFormat sdf = new SimpleDateFormat(pattern);

        if (!StringUtil.isEmpty(time)) {
            return sdf.parse(time);
        }

        return null;

    }


    public static String getDateStr(Date date, String pattern) {

        SimpleDateFormat sdf = new SimpleDateFormat(pattern);

        return sdf.format(date);

    }

}


