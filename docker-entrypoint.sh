#!/bin/bash

# 等待依赖服务启动
echo "等待依赖服务启动..."

# 等待Zookeeper
echo "等待Zookeeper集群..."
until nc -z zk1 2181 && nc -z zk2 2181 && nc -z zk3 2181; do
  echo "Zookeeper未就绪，等待5秒..."
  sleep 5
done
echo "Zookeeper集群已就绪"

# 等待MongoDB
echo "等待MongoDB副本集..."
until nc -z mongodb1 27017 && nc -z mongodb2 27017 && nc -z mongodb3 27017; do
  echo "MongoDB未就绪，等待5秒..."
  sleep 5
done
echo "MongoDB副本集已就绪"

# 等待Redis
echo "等待Redis集群..."
until nc -z redis-cluster-1 6379 && nc -z redis-cluster-2 6379 && nc -z redis-cluster-3 6379; do
  echo "Redis未就绪，等待5秒..."
  sleep 5
done
echo "Redis集群已就绪"

# 等待RocketMQ
echo "等待RocketMQ..."
until nc -z rocketmq-nameserver1 9876 && nc -z rocketmq-nameserver2 9876; do
  echo "RocketMQ未就绪，等待5秒..."
  sleep 5
done
echo "RocketMQ已就绪"

# 设置JVM参数
export JAVA_OPTS="$JAVA_OPTS -Djava.security.egd=file:/dev/./urandom"
export JAVA_OPTS="$JAVA_OPTS -Dspring.profiles.active=cluster"
export JAVA_OPTS="$JAVA_OPTS -Dnode.id=${NODE_ID:-1}"
export JAVA_OPTS="$JAVA_OPTS -Dcluster.enabled=${CLUSTER_ENABLED:-true}"

# 启动应用
echo "启动Shiku IM服务器 (节点ID: ${NODE_ID:-1})..."
exec java $JAVA_OPTS -jar /app/shiku-im.jar $APP_OPTS
