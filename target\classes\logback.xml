<configuration scan="false" scanPeriod="60 seconds" debug="false">
    <property resource="logback.properties"/>

    <contextName>${context.name}</contextName>                         <!-- 本项目的名字 -->

    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${conversion.pattern}</pattern>
        </encoder>
    </appender>

    <logger name="org.mongodb" level="ERROR"/>
    <logger name="io.netty" level="ERROR"/>
    <logger name="io.netty.channel.nio" level="ERROR"/>
    <logger name="org.redisson" level="ERROR"/>
    <logger name="org.tio" level="ERROR"/>


    <!-- root file 日志 -->
    <appender name="root-file-error"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.dir}/error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.dir}/error/error.%d{${rolling.policy.file.name.pattern}}.%i.log.zip</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${max.file.size}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>${max.history}</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${conversion.pattern}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <appender name="root-file-warn"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.dir}/warn.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.dir}/warn/warn.%d{${rolling.policy.file.name.pattern}}.%i.log.zip</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${max.file.size}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>${max.history}</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${conversion.pattern}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>warn</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <appender name="root-file-info"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.dir}/info.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.dir}/info/info.%d{${rolling.policy.file.name.pattern}}.%i.log.zip</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${max.file.size}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>${max.history}</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${conversion.pattern}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <appender name="root-file-debug"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.dir}/debug.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.dir}/debug/debug.%d{${rolling.policy.file.name.pattern}}.%i.log.zip</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${max.file.size}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>${max.history}</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${conversion.pattern}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>debug</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>
    <root level="${root.level}">
        <appender-ref ref="console"/>
        <appender-ref ref="root-file-error"/>
        <appender-ref ref="root-file-warn"/>
        <appender-ref ref="root-file-info"/>
        <appender-ref ref="root-file-debug"/>
    </root>


    <!-- 跟踪客户端行为 -->
    <appender name="tio-client-trace-log-appender" class="ch.qos.logback.classic.sift.SiftingAppender">
        <discriminator>
            <Key>tio_client</Key>
            <DefaultValue>unknown</DefaultValue>
        </discriminator>

        <sift>
            <appender name="${tio_client}" class="ch.qos.logback.core.rolling.RollingFileAppender">
                <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
                    <fileNamePattern>${log.dir}/tio/client-trace/${tio_client}_%d{yyyyMMdd}.log</fileNamePattern>
                    <maxFileSize>20MB</maxFileSize>
                </rollingPolicy>
                <Append>false</Append>
                <layout class="ch.qos.logback.classic.PatternLayout">
                    <pattern>%m%n</pattern>
                </layout>
            </appender>
        </sift>
    </appender>

    <logger name="tio-client-trace-log" additivity="false">
        <level value="info"/>
        <appender-ref ref="tio-client-trace-log-appender"/>
    </logger>


    <appender name="root-file-debug"
              class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.dir}/debug.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.dir}/debug/debug.%d{${rolling.policy.file.name.pattern}}.%i.log.zip</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${max.file.size}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>${max.history}</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${conversion.pattern}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>debug</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>


    <!-- ip访问打印  -->
    <appender name="tio-ip-trace-log-appender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.dir}/ip.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.dir}/ip.%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${max.file.size}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>${max.history}</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d %m%n</pattern>
        </encoder>
    </appender>
    <logger name="tio-ip-trace-log" additivity="false">
        <level value="info"/>
        <appender-ref ref="tio-ip-trace-log-appender"/>
    </logger>

    <!-- chatxxxx日志打印  -->
    <appender name="tio-chatxxxx-trace-log-appender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.dir}/chatxxxx.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.dir}/chatxxxx.%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${max.file.size}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>${max.history}</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d%n %m%n</pattern>
        </encoder>
    </appender>
    <logger name="tio-chatxxxx-trace-log" additivity="false">
        <level value="info"/>
        <appender-ref ref="tio-chatxxxx-trace-log-appender"/>
    </logger>

    <!-- addressxxxx日志打印  -->
    <appender name="tio-addressxxxx-trace-log-appender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.dir}/addressxxxx.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.dir}/addressxxxx.%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${max.file.size}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>${max.history}</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%d%n %m%n</pattern>
        </encoder>
    </appender>
    <logger name="tio-addressxxxx-trace-log" additivity="false">
        <level value="info"/>
        <appender-ref ref="tio-addressxxxx-trace-log-appender"/>
    </logger>

    <!-- userAgentxxxx 日志打印  -->
    <appender name="tio-userAgentxxxx-trace-log-appender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.dir}/userAgentxxxx.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.dir}/userAgentxxxx.%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${max.file.size}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>${max.history}</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%m%n</pattern>
        </encoder>
    </appender>
    <logger name="tio-userAgentxxxx-trace-log" additivity="false">
        <level value="info"/>
        <appender-ref ref="tio-userAgentxxxx-trace-log-appender"/>
    </logger>

    <!-- ipblacklistxxxx ip黑名单  -->
    <appender name="tio-ipblacklistxxxx-trace-log-appender" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.dir}/ipblacklistxxxx.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.dir}/ipblacklistxxxx.%d{yyyy-MM-dd}.%i.log.zip</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy
                    class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>${max.file.size}</maxFileSize>
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>${max.history}</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%m%n</pattern>
        </encoder>
    </appender>
    <logger name="tio-ipblacklistxxxx-trace-log" additivity="false">
        <level value="info"/>
        <appender-ref ref="tio-ipblacklistxxxx-trace-log-appender"/>
    </logger>

    <logger name="org.apache.zookeeper" level="ERROR"/>

</configuration>