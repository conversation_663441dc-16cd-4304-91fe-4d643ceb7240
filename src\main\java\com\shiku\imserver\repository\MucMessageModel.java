
package com.shiku.imserver.repository;

public class MucMessageModel {
    private String body;
    private String message;
    private String nickname;
    private String room_id;
    private String room_jid;
    private Long sender;


    public void setBody(String body) {
        this.body = body;
    }

    private String sender_jid;
    private Long ts;
    private String messageId;
    private String content;
    private Integer contentType;
    private Double timeSend;
    private long deleteTime;

    public void setMessage(String message) {
        this.message = message;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public void setRoom_id(String room_id) {
        this.room_id = room_id;
    }

    public void setRoom_jid(String room_jid) {
        this.room_jid = room_jid;
    }

    public void setSender(Long sender) {
        this.sender = sender;
    }

    public void setSender_jid(String sender_jid) {
        this.sender_jid = sender_jid;
    }

    public void setTs(Long ts) {
        this.ts = ts;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public void setContentType(Integer contentType) {
        this.contentType = contentType;
    }

    public void setTimeSend(Double timeSend) {
        this.timeSend = timeSend;
    }

    public void setDeleteTime(long deleteTime) {
        this.deleteTime = deleteTime;
    }

    public void setIsEncrypt(byte isEncrypt) {
        this.isEncrypt = isEncrypt;
    }


    public String getBody() {

        return this.body;

    }

    public String getMessage() {
        return this.message;
    }


    public String getNickname() {
        return this.nickname;
    }


    public String getRoom_id() {
        return this.room_id;
    }

    public String getRoom_jid() {

        return this.room_jid;

    }


    public Long getSender() {
        return this.sender;
    }


    public String getSender_jid() {
        return this.sender_jid;
    }


    public Long getTs() {
        return this.ts;
    }


    public String getMessageId() {
        return this.messageId;
    }

    public String getContent() {

        return this.content;

    }


    public Integer getContentType() {

        return this.contentType;

    }

    public Double getTimeSend() {

        return this.timeSend;

    }

    public long getDeleteTime() {

        return this.deleteTime;

    }

    private byte isEncrypt = 0;

    public byte getIsEncrypt() {
        return this.isEncrypt;
    }


    public MucMessageModel(String room_id, String room_jid, Long sender, String sender_jid, String nickname, String body, String message, Long ts, String content) {

        this.room_id = room_id;

        this.room_jid = room_jid;

        this.sender = sender;

        this.sender_jid = sender_jid;

        this.nickname = nickname;

        this.body = body;

        this.message = message;

        this.ts = ts;

        this.content = content;

    }


    public MucMessageModel() {
    }
}


