
package com.shiku.commons.thread.pool;


import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;


public class DefaultThreadFactory
        implements ThreadFactory {
    private static Map<String, DefaultThreadFactory> mapOfNameAndThreadFactory = new HashMap<>();


    private static Map<String, AtomicInteger> mapOfNameAndAtomicInteger = new HashMap<>();


    public static DefaultThreadFactory getInstance(String threadName) {

        return getInstance(threadName, Integer.valueOf(5));

    }


    public static DefaultThreadFactory getInstance(String threadName, Integer priority) {

        DefaultThreadFactory defaultThreadFactory = mapOfNameAndThreadFactory.get(threadName);

        if (defaultThreadFactory == null) {

            defaultThreadFactory = new DefaultThreadFactory();

            if (priority != null) {

                defaultThreadFactory.priority = priority.intValue();

            }


            defaultThreadFactory.setThreadName(threadName);

            mapOfNameAndThreadFactory.put(threadName, defaultThreadFactory);

            mapOfNameAndAtomicInteger.put(threadName, new AtomicInteger());

        }

        return defaultThreadFactory;

    }


    private String threadPoolName = null;


    private int priority = 5;


    public String getThreadPoolName() {

        return this.threadPoolName;

    }


    @Override
    public Thread newThread(Runnable r) {

        Thread thread = new Thread(r);

        thread.setName(getThreadPoolName() + "-" + ((AtomicInteger) mapOfNameAndAtomicInteger.get(getThreadPoolName())).incrementAndGet());

        thread.setPriority(this.priority);

        return thread;

    }


    public void setThreadName(String threadName) {

        this.threadPoolName = threadName;

    }

}


