
package com.shiku.utils.encrypt;


import com.shiku.utils.Base64;

import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;


public class MAC {

    public static byte[] encode(byte[] data, String key) {

        return encode(data, key.getBytes());

    }


    public static byte[] encode(byte[] data, byte[] key) {

        try {

            SecretKey secretKey = new SecretKeySpec(key, "HmacMD5");


            Mac mac = Mac.getInstance(secretKey.getAlgorithm());


            mac.init(secretKey);


            return mac.doFinal(data);

        } catch (Exception e) {

            throw new RuntimeException(e);

        }

    }


    public static String encodeBase64(byte[] data, String key) {

        return encodeBase64(data, key.getBytes());

    }


    public static String encodeBase64(byte[] data, byte[] key) {

        return Base64.encode(encode(data, key));

    }

}


