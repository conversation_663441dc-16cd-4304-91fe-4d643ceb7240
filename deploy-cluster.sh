#!/bin/bash

echo "========== Shiku IM 集群部署脚本 =========="

# 检查Docker和Docker Compose
if ! command -v docker &> /dev/null; then
    echo "错误: Docker未安装"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "错误: Docker Compose未安装"
    exit 1
fi

# 构建应用
echo "1. 构建应用..."
mvn clean package -DskipTests

if [ $? -ne 0 ]; then
    echo "错误: Maven构建失败"
    exit 1
fi

# 停止现有容器
echo "2. 停止现有容器..."
docker-compose -f docker-compose-cluster.yml down

# 清理旧镜像（可选）
read -p "是否清理旧的Docker镜像? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "清理旧镜像..."
    docker system prune -f
fi

# 启动基础设施服务
echo "3. 启动基础设施服务..."
docker-compose -f docker-compose-cluster.yml up -d zk1 zk2 zk3
echo "等待Zookeeper集群启动..."
sleep 30

docker-compose -f docker-compose-cluster.yml up -d mongodb1 mongodb2 mongodb3
echo "等待MongoDB副本集启动..."
sleep 30

# 初始化MongoDB副本集
echo "4. 初始化MongoDB副本集..."
docker exec mongodb1 mongo --eval "
rs.initiate({
  _id: 'rs0',
  members: [
    {_id: 0, host: 'mongodb1:27017'},
    {_id: 1, host: 'mongodb2:27017'},
    {_id: 2, host: 'mongodb3:27017'}
  ]
})
"

sleep 20

# 启动Redis集群
echo "5. 启动Redis集群..."
docker-compose -f docker-compose-cluster.yml up -d redis-cluster-1 redis-cluster-2 redis-cluster-3
sleep 20

# 初始化Redis集群
echo "6. 初始化Redis集群..."
docker exec redis-cluster-1 redis-cli --cluster create \
  redis-cluster-1:6379 redis-cluster-2:6379 redis-cluster-3:6379 \
  --cluster-replicas 0 --cluster-yes

# 启动RocketMQ
echo "7. 启动RocketMQ..."
docker-compose -f docker-compose-cluster.yml up -d rocketmq-nameserver1 rocketmq-nameserver2
sleep 20
docker-compose -f docker-compose-cluster.yml up -d rocketmq-broker
sleep 20

# 启动IM服务器集群
echo "8. 启动IM服务器集群..."
docker-compose -f docker-compose-cluster.yml up -d shiku-im-1 shiku-im-2 shiku-im-3
sleep 30

# 启动负载均衡器
echo "9. 启动负载均衡器..."
docker-compose -f docker-compose-cluster.yml up -d nginx

# 检查服务状态
echo "10. 检查服务状态..."
docker-compose -f docker-compose-cluster.yml ps

echo ""
echo "========== 部署完成 =========="
echo "服务访问地址:"
echo "- TCP连接: localhost:5666"
echo "- WebSocket: ws://localhost:5280"
echo "- HTTP API: http://localhost:80"
echo ""
echo "管理界面:"
echo "- MongoDB: ************************************************************************"
echo "- Redis: redis://localhost:6379 (密码: redis_password_2024)"
echo "- Zookeeper: localhost:2181,localhost:2182,localhost:2183"
echo ""
echo "查看日志: docker-compose -f docker-compose-cluster.yml logs -f [服务名]"
echo "停止集群: docker-compose -f docker-compose-cluster.yml down"
