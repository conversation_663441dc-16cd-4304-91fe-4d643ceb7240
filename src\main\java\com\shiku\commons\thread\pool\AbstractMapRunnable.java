
package com.shiku.commons.thread.pool;


import java.util.concurrent.ConcurrentHashMap;


public abstract class AbstractMapRunnable<T>
        extends BaseRunnable {
    protected ConcurrentHashMap<String, T> maps = new ConcurrentHashMap<>();

    protected long sleep = 3000L;


    public void setSleep(long sleep) {

        this.sleep = sleep;

    }


    protected int batchSize = 20;


    public void setBatchSize(int batchSize) {

        this.batchSize = batchSize;

    }


    public abstract void runTask();


    @Override
    public void run() {

        while (true) {

            try {

                synchronized (this.maps) {

                    if (this.maps.isEmpty()) {

                        this.maps.wait();

                    }

                }

                this.loopCount.set(0);

                runTask();

            } catch (Exception e) {

                e.printStackTrace();

            }

        }

    }


    public void addMsg(String key, T t) {

        synchronized (this.maps) {

            this.maps.put(key, t);

            this.maps.notifyAll();

        }

    }


    public void clearMsgQueue() {

        this.maps.clear();

    }


    public boolean isNeededExecute() {

        return !this.maps.isEmpty();

    }

}


