
package com.shiku.imserver.message;

import com.shiku.imserver.common.message.AbstractMessage;
import com.shiku.imserver.common.message.AuthRespMessage;
import com.shiku.imserver.common.message.ErrorMessage;
import com.shiku.imserver.common.message.GroupMessageResp;
import com.shiku.imserver.common.message.MessageHead;
import com.shiku.imserver.common.message.MessageReceiptStatus;
import com.shiku.imserver.common.message.PacketVO;
import com.shiku.imserver.common.message.SuccessMessage;
import com.shiku.imserver.common.packets.ImPacket;
import com.shiku.imserver.common.proto.MessageProBuf.AuthRespMessageProBuf;
import com.shiku.imserver.common.proto.MessageProBuf.ChatMessage;
import com.shiku.imserver.common.proto.MessageProBuf.CommonErrorProBuf;
import com.shiku.imserver.common.proto.MessageProBuf.CommonSuccessProBuf;
import com.shiku.imserver.common.utils.ProBufUtils;
import com.shiku.imserver.common.utils.StringUtils;
import org.tio.core.ChannelContext;

public class MessageFactory {
    public MessageFactory() {
    }

    public static PacketVO createSuccessPacket(AbstractMessage message) {
        PacketVO packet = null;
        SuccessMessage result = new SuccessMessage();
        result.setMessageHead(copyeMessageHead(message.getMessageHead()));
        result.getMessageHead().setChatType((byte) 10);
        packet = ProBufUtils.encodePacketVO(result, CommonSuccessProBuf.getDescriptor());
        packet.setCmd((short) 100);
        return packet;
    }

    public static ImPacket createSuccessIMPacket(AbstractMessage message) {
        ImPacket packet = null;
        SuccessMessage result = new SuccessMessage();
        result.setMessageHead(copyeMessageHead(message.getMessageHead()));
        result.getMessageHead().setChatType((byte) 10);
        packet = ProBufUtils.encodeImPacket(result, CommonSuccessProBuf.getDescriptor());
        packet.setCommand((short) 100);
        return packet;
    }

    public static ImPacket createReceiptIMPacket(AbstractMessage message) {
        ImPacket packet = null;
        MessageReceiptStatus result = new MessageReceiptStatus();
        result.setMessageHead(copyeMessageHead(message.getMessageHead()));
        result.getMessageHead().setChatType((byte) 11);
        result.getMessageHead().setMessageId(StringUtils.newStanzaId());
        result.setMessageId(message.getMessageHead().getMessageId());
        packet = ProBufUtils.encodeImPacket(result, CommonSuccessProBuf.getDescriptor());
        packet.setCommand((short) 11);
        return packet;
    }

    public static MessageHead createMessageHead(ChannelContext channelContext) {
        MessageHead head = new MessageHead();
        head.setTo(channelContext.userid);
        return head;
    }

    public static PacketVO createErrorPacket(MessageHead messageHead, short code) {
        return createErrorPacket(messageHead, code, (String) null);
    }

    public static PacketVO createErrorPacket(MessageHead messageHead, String arg) {
        return createErrorPacket(messageHead, (short) -1, arg);
    }

    public static PacketVO createErrorPacket(MessageHead messageHead) {
        return createErrorPacket(messageHead, (short) -1, (String) null);
    }

    public static PacketVO createErrorPacket(MessageHead messageHead, short code, String arg) {
        PacketVO packet = null;
        ErrorMessage errorMessage = new ErrorMessage();
        errorMessage.setMessageHead(copyeMessageHead(messageHead));
        errorMessage.setCode(code);
        errorMessage.setArg(arg);
        packet = ProBufUtils.encodePacketVO(errorMessage, CommonErrorProBuf.getDescriptor());
        packet.setCmd((short) -1);
        return packet;
    }

    public static PacketVO createAuthRespPacket(AuthRespMessage message) {
        PacketVO packet = null;
        message.getMessageHead().setChatType((byte) 10);
        packet = ProBufUtils.encodePacketVO(message, AuthRespMessageProBuf.getDescriptor());
        packet.setCmd((short) 6);
        return packet;
    }

    public static ImPacket createAuthRespIMPacket(AuthRespMessage message) {
        ImPacket packet = null;
        message.getMessageHead().setChatType((byte) 10);
        packet = ProBufUtils.encodeImPacket(message, AuthRespMessageProBuf.getDescriptor());
        packet.setCommand((short) 6);
        return packet;
    }

    public static ImPacket createLoginConflict(ErrorMessage message) {
        ImPacket packet = null;
        message.getMessageHead().setChatType((byte) 10);
        packet = ProBufUtils.encodeImPacket(message, CommonErrorProBuf.getDescriptor());
        packet.setCommand((short) -3);
        return packet;
    }

    public static ImPacket convertToImPacket(AbstractMessage message) {
        ImPacket packet = ProBufUtils.encodeImPacket(message, ChatMessage.getDescriptor());
        packet.setCommand((short) 10);
        return packet;
    }

    public static MessageHead copyeMessageHead(MessageHead messageHead) {
        MessageHead head = new MessageHead();
        head.setChatType(messageHead.getChatType());
        head.setMessageId(messageHead.getMessageId());
        head.setFrom(messageHead.getFrom());
        head.setTo(messageHead.getTo());
        return head;
    }

    public static GroupMessageResp createGroupRequestResult(AbstractMessage message, String jid, boolean isExit) {
        GroupMessageResp result = new GroupMessageResp();
        result.setMessageHead(copyeMessageHead(message.getMessageHead()));
        result.getMessageHead().setChatType((byte) 10);
        result.setJid(jid);
        result.setExit(isExit);
        return result;
    }
}
