
package com.shiku.imserver.common.utils;


import com.alibaba.fastjson.JSONObject;
import com.google.protobuf.Descriptors;
import com.google.protobuf.DynamicMessage;
import com.google.protobuf.Message;
import com.google.protobuf.MessageOrBuilder;
import com.google.protobuf.util.JsonFormat;
import com.shiku.imserver.common.message.AbstractBaseMessage;
import com.shiku.imserver.common.message.MessageHead;
import com.shiku.imserver.common.message.PacketVO;
import com.shiku.imserver.common.packets.ImPacket;
import com.shiku.imserver.common.proto.MessageProBuf;
import com.shiku.imserver.common.tcp.TcpPacket;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class ProBufUtils {
    private static final Logger logger = LoggerFactory.getLogger(ProBufUtils.class);


    public static <T> T decoderMessageBody(byte[] bytes, Descriptors.Descriptor descriptor, Class<T> classz) {

        T message = null;

        try {

            DynamicMessage parseFrom = DynamicMessage.parseFrom(descriptor, bytes);

            String msgStr = JsonFormat.printer().print((MessageOrBuilder) parseFrom);

            message = (T) JSONObject.parseObject(msgStr, classz);

            if (null == message) {

                logger.error("message is null");

            }

        } catch (Exception e) {


            e.printStackTrace();

        }

        return message;

    }


    public static <T extends com.shiku.imserver.common.message.AbstractMessage> byte[] encodeMessageBody(T message, Descriptors.Descriptor descriptor) {

        byte[] bytes = null;

        try {

            DynamicMessage.Builder builder = DynamicMessage.newBuilder(descriptor);

            JsonFormat.parser().merge(message.toString(), (Message.Builder) builder);

            bytes = builder.build().toByteArray();

        } catch (Exception e) {

            e.printStackTrace();

        }

        return bytes;

    }


    public static <T> PacketVO encodePacketVO(T message, Descriptors.Descriptor descriptor) {

        byte[] bytes = null;

        try {

            DynamicMessage.Builder builder = DynamicMessage.newBuilder(descriptor);

            JsonFormat.parser().merge(message.toString(), (Message.Builder) builder);

            bytes = builder.build().toByteArray();

        } catch (Exception e) {

            e.printStackTrace();

        }

        if (null != bytes) {

            return new PacketVO(bytes);

        }

        return null;

    }


    public static ImPacket encodeImPacket(AbstractBaseMessage message, Descriptors.Descriptor descriptor) {

        byte[] bytes = null;

        try {

            DynamicMessage.Builder builder = DynamicMessage.newBuilder(descriptor);

            JsonFormat.parser().merge(message.toString(), (Message.Builder) builder);

            bytes = builder.build().toByteArray();

        } catch (Exception e) {

            e.printStackTrace();

        }

        if (null != bytes) {

            return new ImPacket(bytes);

        }

        return null;

    }


    public static TcpPacket encodeTcpPacket(AbstractBaseMessage message, Descriptors.Descriptor descriptor) {

        byte[] bytes = null;

        try {

            DynamicMessage.Builder builder = DynamicMessage.newBuilder(descriptor);

            JsonFormat.parser().merge(message.toString(), (Message.Builder) builder);

            bytes = builder.build().toByteArray();

        } catch (Exception e) {

            e.printStackTrace();

        }

        if (null != bytes) {

            return new TcpPacket(bytes);

        }

        return null;

    }


    public static <T> MessageProBuf.MessageHead encodeProBufMessageHead(MessageHead messageHead) {

        MessageProBuf.MessageHead result = null;


        try {

            DynamicMessage.Builder builder = DynamicMessage.newBuilder(MessageProBuf.MessageHead.getDescriptor());

            JsonFormat.parser().merge(messageHead.toString(), (Message.Builder) builder);

            result = MessageProBuf.MessageHead.parseFrom(builder.build().toByteArray());

            if (null == result) {

                logger.error("MessageHead is null");

            }

        } catch (Exception e) {

            e.printStackTrace();

        }

        return result;

    }

}


