package com.shiku.imclient;

import com.shiku.imserver.common.constant.KConstants;
import com.shiku.imserver.common.message.AuthMessage;
import com.shiku.imserver.common.message.ChatMessage;
import com.shiku.imserver.common.message.MessageHead;
import com.shiku.imserver.common.tcp.TcpPacket;
import com.shiku.imserver.common.utils.Callback;
import com.shiku.imserver.common.utils.ProBufUtils;
import com.shiku.imserver.common.utils.ThreadUtil;

import java.util.Scanner;

import org.tio.client.ClientChannelContext;
import org.tio.client.ClientGroupContext;
import org.tio.client.ReconnConf;
import org.tio.client.TioClient;
import org.tio.client.intf.ClientAioHandler;
import org.tio.client.intf.ClientAioListener;
import org.tio.core.ChannelContext;
import org.tio.core.Node;
import org.tio.core.Tio;
import org.tio.utils.hutool.StrUtil;

public class IMClientStarter2 {
    static String serverIp = "*************";
    static int serverPort = 5666;
    private static Node serverNode;
    private static ClientChannelContext context;

    public IMClientStarter2() {
    }

    public static void processCommand(String line) throws Exception {
        if (!StrUtil.isBlank(line)) {
            MessageHead messageHead = new MessageHead();
            ChatMessage message = new ChatMessage();
            messageHead.setChatType((byte) 1);
            messageHead.setFrom("20000/Server");
            messageHead.setTo("10000");
            message.setFromUserId("20000");
            message.setFromUserName("20000");
            message.setToUserId("10000");
            message.setToUserName("10000");
            message.setContent(line);
            message.setType((short) 1);
            message.setMessageHead(messageHead);
            byte[] bytes = ProBufUtils.encodeMessageBody(message, com.shiku.imserver.common.proto.MessageProBuf.ChatMessage.getDescriptor());
            TcpPacket packet = new TcpPacket((short) 10, bytes);
            Tio.send(context, packet);
        }
    }

    public static void main(String[] args) throws Exception {
        context = getClientChannelContext();
        authUser();
        KConstants.isDebug = false;
    }

    public static void authUser() {
        MessageHead messageHead = new MessageHead();
        ChatMessage message = new ChatMessage();
        messageHead.setChatType((byte) 1);
        messageHead.setFrom("20000/Server");
        messageHead.setTo("10000");
        message.setFromUserId("20000");
        message.setFromUserName("20000");
        message.setToUserId("10000");
        message.setToUserName("10000");
        message.setContent("1111111");
        message.setType((short) 1);
        message.setMessageHead(messageHead);
        AuthMessage authMessage = new AuthMessage();
        authMessage.setPassword("1234567");
        authMessage.setToken("token2");
        authMessage.setMessageHead(messageHead);
        byte[] bytes = ProBufUtils.encodeMessageBody(authMessage, com.shiku.imserver.common.proto.MessageProBuf.AuthMessage.getDescriptor());
        TcpPacket packet = new TcpPacket((short) 5, bytes);
        Tio.send(context, packet);
    }

    public static void whileConnect() {
        for (int i = 0; i < 1000; ++i) {
            ThreadUtil.executeInThread(new Callback() {
                @Override
                public void execute(Object obj) {
                    ClientChannelContext context = IMClientStarter2.getClientChannelContext();
                }
            });
        }

    }

    public static ClientGroupContext getClientGroupContext() {
        ClientAioHandler tioClientHandler = new BaseClientHandler() {
            @Override
            public void handlerReceipt(String messageId) {
            }
        };
        ClientAioListener aioListener = new BaseClientListener() {
            @Override
            public AuthMessage authUserMessage(ChannelContext channelContext, BaseIMClient imClient) {
                return null;
            }
        };
        ReconnConf reconnConf = new ReconnConf(5000L);
        ClientGroupContext clientGroupContext = new ClientGroupContext(tioClientHandler, aioListener, reconnConf);
        return clientGroupContext;
    }

    public static ClientChannelContext getClientChannelContext() {
        ClientChannelContext clientChannel = null;

        try {
            ClientGroupContext context = getClientGroupContext();
            clientChannel = (new TioClient(context)).connect(serverNode);
            clientChannel.setHeartbeatTimeout(10000L);
        } catch (Exception var2) {
            var2.printStackTrace();
        }

        return clientChannel;
    }

    public static void whileSendMsg(ClientChannelContext context) {
        int i = 0;
        long start = System.currentTimeMillis();
        int count = '썐';

        for (int j = 0; j < count; ++j) {
            MessageHead messageHead = new MessageHead();
            ChatMessage message = new ChatMessage();
            messageHead.setChatType((byte) 1);
            messageHead.setFrom("20000/Server");
            messageHead.setTo("10000");
            message.setFromUserId("10000");
            message.setFromUserName("10000");
            message.setToUserId("20000");
            message.setToUserName("20000");
            message.setContent(i + "====>");
            message.setType((short) 1);
            message.setMessageHead(messageHead);
            TcpPacket packet = ProBufUtils.encodeTcpPacket(message, com.shiku.imserver.common.proto.MessageProBuf.ChatMessage.getDescriptor());
            packet.setCommand((short) 10);
            Tio.send(context, packet);

            try {
                Thread.sleep(2L);
            } catch (InterruptedException var10) {
                var10.printStackTrace();
            }

            ++i;
        }

        long timeCount = (System.currentTimeMillis() - start) / 1000L;
        System.out.println(" 总数量  " + count + " 发送总耗时 " + timeCount + " 平均耗时  " + (long) count / timeCount);
    }

    public static void command() throws Exception {
        Scanner sc = new Scanner(System.in);
        int i = 1;
        StringBuilder sb = new StringBuilder();
        sb.append("使用指南:\r\n");
        StringBuilder var10001 = new StringBuilder();
        int var4 = i + 1;
        sb.append(var10001.append(i).append("、需要帮助，输入 '?'.\r\n").toString());
        sb.append(var4++ + "、登录，输入 'login loginname password'.\r\n");
        sb.append(var4++ + "、进入群组，输入 'join group1'.\r\n");
        sb.append(var4++ + "、群聊，输入 'groupMsg group1 text'.\r\n");
        sb.append(var4++ + "、点对点聊天，输入 'p2pMsg loginname text'.\r\n");
        sb.append(var4++ + "、退出程序，输入 'exit'.\r\n");
        System.out.println(sb);

        for (String line = sc.nextLine(); !"exit".equalsIgnoreCase(line); line = sc.nextLine()) {
            if ("?".equals(line)) {
                System.out.println(sb);
            }

            processCommand(line);
        }

        System.out.println("Thanks for using! bye bye.");
        System.exit(0);
    }

    static {
        serverNode = new Node(serverIp, serverPort);
    }
}
