
package com.shiku.imserver.message.hander;

import com.shiku.imserver.common.annotation.MessageCommandAnnotation;
import com.shiku.imserver.common.hander.AbstractMessageHandler;
import com.shiku.imserver.common.message.PacketVO;
import com.shiku.imserver.common.message.ServerReqMessage;
import com.shiku.imserver.common.packets.ImPacket;
import com.shiku.imserver.common.proto.MessageProBuf.ServerReqMessageProBuf;
import com.shiku.imserver.common.utils.ProBufUtils;
import com.shiku.imserver.service.IMBeanUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.core.ChannelContext;

public class ServerMessageHandler extends AbstractMessageHandler {
    private static final Logger logger = LoggerFactory.getLogger(ServerMessageHandler.class);

    public ServerMessageHandler() {
    }

    @MessageCommandAnnotation(
            command = 200
    )
    public PacketVO serverMessageHandler(ImPacket packet, ChannelContext channelContext) {
        try {
            ServerReqMessage message = (ServerReqMessage) ProBufUtils.decoderMessageBody(packet.getBytes(), ServerReqMessageProBuf.getDescriptor(), ServerReqMessage.class);
            if (null == message) {
                return null;
            }

            String from = message.getMessageHead().getFrom();
            switch (message.getType()) {
                case 1:
                    IMBeanUtils.getRedisService().initConfig();
                case 2:
                    IMBeanUtils.getBeanManager().getServiceRepository().getAllKeyWord();
            }
        } catch (Exception var5) {
            var5.printStackTrace();
        }

        return null;
    }
}
