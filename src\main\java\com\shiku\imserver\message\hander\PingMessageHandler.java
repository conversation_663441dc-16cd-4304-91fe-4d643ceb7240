
package com.shiku.imserver.message.hander;

import com.shiku.imserver.common.annotation.MessageCommandAnnotation;
import com.shiku.imserver.common.hander.AbstractMessageHandler;
import com.shiku.imserver.common.message.PacketVO;
import com.shiku.imserver.common.message.PingMessage;
import com.shiku.imserver.common.packets.ImPacket;
import com.shiku.imserver.common.proto.MessageProBuf.PingMessageProBuf;
import com.shiku.imserver.common.utils.ProBufUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.core.ChannelContext;

public class PingMessageHandler extends AbstractMessageHandler {
    private static final Logger logger = LoggerFactory.getLogger(PingMessageHandler.class);

    public PingMessageHandler() {
    }

    @MessageCommandAnnotation(
            command = 99
    )
    public PacketVO pingMessageHandler(ImPacket packet, ChannelContext channelContext) {
        try {
            PingMessage message = (PingMessage) ProBufUtils.decoderMessageBody(packet.getBytes(), PingMessageProBuf.getDescriptor(), PingMessage.class);
            if (null == message) {
                return null;
            }

            String var4 = message.getMessageHead().getFrom();
        } catch (Exception var5) {
            var5.printStackTrace();
        }

        return null;
    }
}
