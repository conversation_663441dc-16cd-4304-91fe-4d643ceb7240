
package com.shiku.imserver.message.processor;

import com.shiku.imserver.common.AbstractService;
import com.shiku.imserver.common.annotation.MessageCommandAnnotation;
import com.shiku.imserver.common.constant.IMLoggers;
import com.shiku.imserver.common.hander.AbstractMessageHandler;
import com.shiku.imserver.common.hander.IMessageHandler;
import com.shiku.imserver.common.message.PacketVO;
import com.shiku.imserver.common.packets.ImPacket;
import com.shiku.imserver.common.scanner.ClassScanner;
import com.shiku.imserver.service.IMBeanUtils;

import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.tio.core.ChannelContext;

public class MessageProcess extends AbstractService {
    public static final Logger logger;
    public ClassScanner classScanner = new ClassScanner();
    public String[] fileNames;
    protected Map<Short, IMessageHandler> handlers = new HashMap();

    public MessageProcess() {
    }

    public void addHandler(short httpCode, IMessageHandler handler) {
        this.handlers.put(httpCode, handler);
    }

    public PacketVO dispatch(ImPacket packet, ChannelContext channelContext) {
        PacketVO result = null;

        try {
            short cmd = packet.getCommand();
            IMessageHandler handler = (IMessageHandler) this.handlers.get(cmd);
            if (null == handler) {
                if (7 != cmd) {
                    logger.error("handler is null userId  {}  cmd {} ", channelContext.userid, packet.getCommand());
                }

                return null;
            } else {
                Method method = handler.getMessageHandler(cmd);
                if (null == method) {
                    logger.error("handler is null userId  {}  cmd {} ", channelContext.userid, packet.getCommand());
                    return null;
                } else {
                    method.setAccessible(true);
                    Object object = method.invoke(handler, packet, channelContext);
                    if (object != null) {
                        result = (PacketVO) object;
                    }

                    return result;
                }
            }
        } catch (Exception var8) {
            var8.printStackTrace();
            return result;
        }
    }

    public void loadPackage(String namespace, String ext) throws Exception {
        if (this.fileNames == null) {
            this.fileNames = this.classScanner.scannerPackage(namespace, ext);
        }

        if (this.fileNames != null) {
            String[] var3 = this.fileNames;
            int var4 = var3.length;

            for (int var5 = 0; var5 < var4; ++var5) {
                String fileName = var3[var5];
                String realClass = namespace + "." + fileName.subSequence(0, fileName.length() - ext.length());
                Class<?> messageClass = Class.forName(realClass);
                logger.info("handler load: " + messageClass);
                IMessageHandler iMessageHandler = this.getMessageHandler(messageClass);
                AbstractMessageHandler handler = (AbstractMessageHandler) iMessageHandler;
                handler.init();
                Method[] methods = messageClass.getMethods();
                Method[] var12 = methods;
                int var13 = methods.length;

                for (int var14 = 0; var14 < var13; ++var14) {
                    Method method = var12[var14];
                    if (method.isAnnotationPresent(MessageCommandAnnotation.class)) {
                        logger.info("handler method name  " + method.getName());
                        MessageCommandAnnotation messageCommandAnnotation = (MessageCommandAnnotation) method.getAnnotation(MessageCommandAnnotation.class);
                        if (messageCommandAnnotation != null) {
                            this.addHandler(messageCommandAnnotation.command(), iMessageHandler);
                        } else {
                            logger.info("handler is null  " + messageClass);
                        }
                    }
                }
            }

            logger.info("handler size is " + this.handlers.size());
        }

    }

    public final IMessageHandler getMessageHandler(Class<?> classes) {
        try {
            if (classes == null) {
                return null;
            } else {
                IMessageHandler messageHandler = (IMessageHandler) classes.newInstance();
                return messageHandler;
            }
        } catch (Exception var3) {
            logger.error("getMessageHandler - classes=" + classes.getName() + ". ", var3);
            return null;
        }
    }

    @Override
    public boolean initialize() {
        try {
            this.loadPackage(IMBeanUtils.getImconfig().getMessageHandlerNameSpace(), ".class");
        } catch (Exception var2) {
            var2.printStackTrace();
        }

        return false;
    }

    static {
        logger = IMLoggers.serverLog;
    }
}
