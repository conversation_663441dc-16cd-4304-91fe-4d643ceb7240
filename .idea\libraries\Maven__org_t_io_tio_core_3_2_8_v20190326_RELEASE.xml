<component name="libraryTable">
  <library name="Maven: org.t-io:tio-core:3.2.8.v20190326-RELEASE">
    <CLASSES>
      <root url="jar://$MAVEN_REPOSITORY$/org/t-io/tio-core/3.2.8.v20190326-RELEASE/tio-core-3.2.8.v20190326-RELEASE.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://$MAVEN_REPOSITORY$/org/t-io/tio-core/3.2.8.v20190326-RELEASE/tio-core-3.2.8.v20190326-RELEASE-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://$MAVEN_REPOSITORY$/org/t-io/tio-core/3.2.8.v20190326-RELEASE/tio-core-3.2.8.v20190326-RELEASE-sources.jar!/" />
    </SOURCES>
  </library>
</component>