
package com.shiku.imserver.repository;


public class LastChatModel {
    private String content;
    private int type;
    private long timeSend;
    private String userId;
    private String jid;
    private int isRoom;


    public void setContent(String content) {
        this.content = content;
    }

    private boolean isEncrypt;
    private String messageId;
    private String from;
    private String to;
    private String fromUserName;
    private String toUserName;

    public void setType(int type) {
        this.type = type;
    }

    public void setTimeSend(long timeSend) {
        this.timeSend = timeSend;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public void setJid(String jid) {
        this.jid = jid;
    }

    public void setIsRoom(int isRoom) {
        this.isRoom = isRoom;
    }

    public void setEncrypt(boolean isEncrypt) {
        this.isEncrypt = isEncrypt;
    }

    public void setMessageId(String messageId) {
        this.messageId = messageId;
    }

    public void setFrom(String from) {
        this.from = from;
    }

    public void setTo(String to) {
        this.to = to;
    }

    public void setFromUserName(String fromUserName) {
        this.fromUserName = fromUserName;
    }

    public void setToUserName(String toUserName) {
        this.toUserName = toUserName;
    }


    public String getContent() {

        return this.content;

    }

    public int getType() {

        return this.type;

    }

    public long getTimeSend() {

        return this.timeSend;

    }

    public String getUserId() {

        return this.userId;

    }


    public String getJid() {

        return this.jid;

    }

    public int getIsRoom() {

        return this.isRoom;

    }

    public boolean isEncrypt() {

        return this.isEncrypt;

    }

    public String getMessageId() {

        return this.messageId;

    }


    public String getFrom() {

        return this.from;

    }

    public String getTo() {

        return this.to;

    }

    public String getFromUserName() {

        return this.fromUserName;

    }

    public String getToUserName() {

        return this.toUserName;

    }

}


