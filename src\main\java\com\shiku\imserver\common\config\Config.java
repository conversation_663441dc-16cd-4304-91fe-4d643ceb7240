
package com.shiku.imserver.common.config;


public class Config {

    public void setBindIp(String bindIp) {

        this.bindIp = bindIp;
    }

    public void setBindPort(Integer bindPort) {
        this.bindPort = bindPort;
    }

    public void setHeartbeatTimeout(long heartbeatTimeout) {
        this.heartbeatTimeout = heartbeatTimeout;
    }

    public void setReadBufferSize(long readBufferSize) {
        this.readBufferSize = readBufferSize;
    }


    protected String bindIp = null;

    public String getBindIp() {
        return this.bindIp;
    }


    protected Integer bindPort = Integer.valueOf(5222);

    public Integer getBindPort() {
        return this.bindPort;
    }


    protected long heartbeatTimeout = 0L;

    public long getHeartbeatTimeout() {
        return this.heartbeatTimeout;
    }


    protected long readBufferSize = 2048L;

    public long getReadBufferSize() {
        return this.readBufferSize;
    }


}


