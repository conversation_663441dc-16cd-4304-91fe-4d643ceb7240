
package com.shiku.imserver;


import com.shiku.imserver.common.ImConfig;
import com.shiku.imserver.common.utils.PropertiesUtil;
import com.shiku.imserver.common.utils.StringUtils;
import com.shiku.imserver.common.ws.IWsMsgHandler;
import com.shiku.imserver.common.ws.WsServerConfig;
import com.shiku.imserver.hander.ImServerHandler;
import com.shiku.imserver.listener.ImServerListener;
import com.shiku.imserver.listener.ShikuGroupListener;
import com.shiku.imserver.service.IMBeanUtils;

import java.io.IOException;
import java.util.Properties;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.core.intf.GroupListener;
import org.tio.server.intf.ServerAioListener;



public class IMServerStarterApplication {
    private static Logger log = LoggerFactory.getLogger(IMServerStarterApplication.class);


    public IMServerStarterApplication() {

        this(null, null);

    }

    private IWsMsgHandler wsMsgHandler = null;

    private ImServerHandler serverHandler = null;

    private ServerAioListener serverListener = null;

    private ImServerGroupContext serverGroupContext = null;

    private ShikuTioServer tioServer = null;


    public static void main(String[] args) throws Exception {

        IMServerStarterApplication IMServerStarterApplication = new IMServerStarterApplication();


        try {

            String propertyFile = null;

            if (0 < args.length) {
                propertyFile = args[0];
            }

            ImConfig config = initImConfig(propertyFile);

            IMBeanUtils.initialize(config, IMServerStarterApplication.getServerGroupContext());


            IMServerStarterApplication.start("", config.getBindPort().intValue());

        } catch (Exception e) {

            log.error(" 启动服务器 异常  {}", e.getMessage());

        }

    }


    public IWsMsgHandler getWsMsgHandler() {

        return this.wsMsgHandler;

    }


    public ImServerGroupContext getServerGroupContext() {

        return this.serverGroupContext;

    }


    public IMServerStarterApplication(WsServerConfig wsServerConfig, IWsMsgHandler wsMsgHandler) {

        try {

            this.wsMsgHandler = wsMsgHandler;

            this.serverHandler = new ImServerHandler();

            this.serverListener = (ServerAioListener) new ImServerListener();

            this.serverGroupContext = new ImServerGroupContext(this.serverHandler, this.serverListener);

            this.serverGroupContext.setHeartbeatTimeout(180000L);

            this.serverGroupContext.setGroupListener((GroupListener) new ShikuGroupListener());


            this.tioServer = new ShikuTioServer(this.serverGroupContext);

        } catch (Exception e) {

            e.printStackTrace();

        }

    }


    public void start(String ip, int port) throws IOException {

        this.tioServer.start(ip, port);

    }


    public static ImConfig initImConfig(String url) throws Exception {

        String propertyFile = "classpath:imserver.properties";

        if (!StringUtils.isEmpty(url)) {

            propertyFile = url;

        }

        Properties properties = PropertiesUtil.loadPropertyFile(propertyFile);

        if (null == properties) {

            log.error("加载配置文件失败  =====》 ");

            throw new Exception("加载配置文件失败  =====》 ");

        }

        ImConfig config = new ImConfig(properties);

        config.initImConfig();

        return config;

    }

}


