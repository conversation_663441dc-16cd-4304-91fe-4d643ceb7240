
package com.shiku.imserver.listener;

import com.shiku.imserver.CoreService;
import com.shiku.imserver.cluster.ImClusterServiceIfc;
import com.shiku.imserver.common.constant.KConstants;
import com.shiku.imserver.common.message.AuthRespMessage;
import com.shiku.imserver.common.packets.IPacket;
import com.shiku.imserver.common.packets.ImPacket;
import com.shiku.imserver.common.utils.StringUtils;
import com.shiku.imserver.hander.BaseServerListener;
import com.shiku.imserver.message.MessageFactory;
import com.shiku.imserver.service.IMBeanUtils;

import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.tio.core.ChannelContext;
import org.tio.core.Tio;
import org.tio.core.intf.Packet;
import org.tio.utils.lock.SetWithLock;

public class ImServerListener extends BaseServerListener {
    Logger logger = LoggerFactory.getLogger(ImServerListener.class);

    public ImServerListener() {
    }

    @Override
    public void onAfterConnected(ChannelContext channelContext, boolean isConnected, boolean isReconnect) {
        try {
            super.onAfterConnected(channelContext, isConnected, isReconnect);
        } catch (Exception var5) {
            this.logger.error(var5.toString(), var5);
        }

    }

    @Override
    public void onAfterSent(ChannelContext channelContext, Packet packet, boolean isSentSuccess) {
        try {
            if (packet instanceof ImPacket) {
                IPacket imPacket = (IPacket) packet;
                if (10 == imPacket.getCommand()) {
                    IMBeanUtils.getReceiptLogicService().willMessageReceipt(channelContext, imPacket.getMessage());
                } else if (-3 == imPacket.getCommand()) {
                    Tio.remove(channelContext, "被挤下线!");
                }
            }

            super.onAfterSent(channelContext, packet, isSentSuccess);
        } catch (Exception var5) {
            this.logger.error(var5.getMessage(), var5);
        }

    }

    @Override
    public void onBeforeClose(ChannelContext channelContext, Throwable throwable, String remark, boolean isRemove) {
        try {
            Object resource = channelContext.getAttribute("resource");
            String from = channelContext.userid + "/" + resource;
            if (KConstants.isDebug) {
                this.logger.info("onBeforeClose ===> to {}", channelContext.userid, from);
            }

            IMBeanUtils.getReceiptLogicService().logout(channelContext);
            SetWithLock<ChannelContext> setWithLock = Tio.getChannelContextsByUserid(channelContext.groupContext, channelContext.userid);
            if (null != setWithLock && 1 < setWithLock.size()) {
                this.logger.info("=====> setWithLock size " + setWithLock.size());
                AuthRespMessage authRespMessage = new AuthRespMessage();
                authRespMessage.createMessageHead();
                authRespMessage.getMessageHead().setFrom(from);
                authRespMessage.getMessageHead().setMessageId(StringUtils.newStanzaId());
                authRespMessage.getMessageHead().setChatType((byte) 10);
                authRespMessage.setStatus((byte) 2);
                Set<String> listResources = CoreService.getChannelContextResources(channelContext.groupContext, channelContext.userid);
                listResources.remove(resource);
                authRespMessage.setResources(StringUtils.listToString(listResources, ","));
                ImPacket result = MessageFactory.createAuthRespIMPacket(authRespMessage);
                CoreService.sendToUserOtherResource(channelContext.groupContext, channelContext.userid, channelContext.getId(), result, false);
                return;
            }

            if (null != resource) {
                ImClusterServiceIfc imClusterService = IMBeanUtils.getBeanManager().getImClusterService();
                if (null != imClusterService) {
                    imClusterService.notifyClusterUserStatus(channelContext.userid, resource.toString(), false);
                }
            }

            IMBeanUtils.getRocketmqService().closeConnection(from);
            super.onBeforeClose(channelContext, throwable, remark, isRemove);
        } catch (Exception var11) {
            this.logger.error(var11.toString(), var11);
        }

    }

    @Override
    public void onAfterDecoded(ChannelContext channelContext, Packet packet, int packetSize) throws Exception {
        try {
            super.onAfterDecoded(channelContext, packet, packetSize);
        } catch (Exception var5) {
            this.logger.error(var5.toString(), var5);
        }

    }

    @Override
    public void onAfterReceivedBytes(ChannelContext channelContext, int receivedBytes) throws Exception {
        try {
            super.onAfterReceivedBytes(channelContext, receivedBytes);
        } catch (Exception var4) {
            this.logger.error(var4.toString(), var4);
        }

    }

    @Override
    public void onAfterHandled(ChannelContext channelContext, Packet packet, long cost) throws Exception {
        try {
            super.onAfterHandled(channelContext, packet, cost);
        } catch (Exception var6) {
            this.logger.error(var6.toString(), var6);
        }

    }
}
